	<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<title>
			<%= htmlWebpackPlugin.options.title %>
		</title>
		<script>
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports(
				'top: constant(a)'))
		</script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
    <!-- 配置H5的 web图标static/logo.png -->
    <link rel="icon" href="<%= BASE_URL %>static/logo.png"/>
		<!-- 手动引入element样式，在main.js中引入会报错-->
		<link rel="stylesheet" href="<%= BASE_URL %>static/plugs/element/index.css"/>
		<!-- 手动引入quill相关的文件-->
		<link rel="stylesheet" href="<%= BASE_URL %>static/plugs/quill/index.css"/>
		<script async src="<%= BASE_URL %>static/plugs/quill/quill.min.js"></script>
		<script async src="<%= BASE_URL %>static/plugs/quill/image-resize.min.js"></script>
	</head>

	<body>
		<noscript>
			<strong>Please enable JavaScript to continue.</strong>
		</noscript>
		<div id="app"></div>
		<!-- built files will be auto injected -->
	</body>

</html>
