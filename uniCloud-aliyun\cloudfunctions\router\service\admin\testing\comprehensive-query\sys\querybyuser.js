module.exports = {
    /**
     * 查询多条记录 分页
     * @url admin/kong/sys/getList 前端调用的url参数地址
     * data 请求参数 说明
     * @param {Number}         pageIndex 当前页码
     * @param {Number}         pageSize  每页显示数量
     * @param {Array<Object>}  sortRule  排序规则
     * @param {object}         formData  查询条件数据源
     * @param {Array<Object>}  columns   查询条件规则
     * res 返回参数说明
     * @param {Number}         code      错误码，0表示成功
     * @param {String}         msg       详细信息
     */
    main: async (event) => {
        let { data = {}, userInfo, util, filterResponse, originalParam } = event;
        let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
        let { uid } = data;
        var $ = _.aggregate; // 聚合查询操作符
        let res = { code: 0, msg: "" };
        // 业务逻辑开始-----------------------------------------------------------
        let dbName = "tm-sample-testing";
        let {
            detection_user, _add_time
        } = data;
        
        console.log(detection_user);
        res = await vk.baseDao.select({
            dbName: dbName,
            pageIndex: 1,
            pageSize: 9999,
            whereJson: {
                detection_user: detection_user,
                _add_time: _.gte(_add_time[0]).lte(_add_time[1]),
                price: _.exists(true),
            },
            // groupJson: {
            //     _id: "$detection_user",
            //     detection_user: $.first("$detection_user"),
            //     price: $.sum("$price"),
            //     // 这里是为了把user_id原样输出
            //     count: $.sum(1), // count记录条数
            // }

        });

        return res;
    },
};
