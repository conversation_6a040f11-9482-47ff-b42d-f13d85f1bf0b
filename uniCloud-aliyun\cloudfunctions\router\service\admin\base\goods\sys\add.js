module.exports = {
	/**
	 * 添加单条数据
	 * @url admin/kong/sys/add 前端调用的url参数地址
	 * data 请求参数 说明
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: 'ok' };
		// 业务逻辑开始-----------------------------------------------------------
		let {
			sort,
			name,
			goods_spe,
			goods_type,
			detection_category,
			thumb,
			is_on,
			type_name,
			detection_category_name,
		} = data;
		// 这里需要把 params1 params2 params3 改成你数据库里允许用户添加的字段
		let dbName = "tm-goods";

		let info = await vk.baseDao.findByWhereJson({
			dbName: dbName,
			whereJson: {
				name: name,
			}
		});
		if (vk.pubfn.isNotNull(info)) {
			res.code = -1;
			res.msg=`${info.name}系统已经存在,请勿重复添加`
			return res;
		} else {
			res.id = await vk.baseDao.add({
				dbName,
				dataJson: {
					sort,
					name,
					goods_spe,
					goods_type,
					detection_category,
					thumb,
					is_on,
					type_name,
					detection_category_name,
				}
			});
			// 业务逻辑结束-----------------------------------------------------------
			return res;
		}

	}
}
