<template>
    <view class="page-body">
        <!-- 页面内容开始 -->

        <!-- 自定义按钮区域开始 -->
        <view class="vk-table-button-box">
            <el-button type="success" size="mini" icon="el-icon-circle-plus-outline" @click="addBtn">添加</el-button>

        </view>
        <!-- 自定义按钮区域结束 -->

        <!-- 表格组件开始 -->
        <vk-data-table ref="table1" size="mini" :action="table1.action" :columns="table1.columns"
            :query-form-param="queryForm1" :right-btns="['detail_auto', 'update', 'delete']" :default-expand-all="true"
            @update="updateBtn" @delete="deleteBtn" @current-change="currentChange" @selection-change="selectionChange"
            :custom-right-btns="table1.customRightBtns">
            <!-- 排序值 -->
            <template v-slot:sort="{ row, column, index }">
                <el-input v-model="row.sort" size="mini" @change="sortChange($event, row)" />
            </template>
        </vk-data-table>
        <!-- 表格组件结束 -->

        <!-- 添加或编辑的弹窗开始 -->
        <vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="700px" mode="form">
            <vk-data-form v-model="form1.data" :rules="form1.props.rules" :action="form1.props.action"
                :form-type="form1.props.formType" :columns='form1.props.columns' label-width="120px"
                @success="formSuccess"></vk-data-form>
        </vk-data-dialog>
        <!-- 添加或编辑的弹窗结束 -->

    </view>
</template>

<script>

var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
var originalForms = {};						// 表单初始化数据


export default {
    components: {

    },
    data() {
        // 页面数据变量
        return {
            // 页面是否请求中或加载中
            loading: false,
            // init请求返回的数据
            data: {

            },
            // 表格相关开始 -----------------------------------------------------------
            table1: {
                // 表格数据请求地址
                action: "admin/base/organization/sys/getList",
                // 表格字段显示规则
                columns: [
                    /* { key:"icon", title:"图标", type:"icon", width:100, defaultValue: "el-icon-tickets"}, */
                    {
                        key: "name", title: "名称", type: "html", width: 600, align: "left"

                    },


                    { key: "comment", title: "备注", type: "text", width: 200, align: "left" },

                    { key: "sort", title: "排序值", type: "number", width: 100 },


                    // { key: "parent_id", title: "父级菜单Id", type: "text", width: 250, align: "left" },
                ],
                // 多选框选中的值
                multipleSelection: [],
                // 当前高亮的记录
                selectItem: "",
                customRightBtns: [
                    {
                        title: '添加下级', type: 'success', icon: 'el-icon-plus',

                        onClick: (item) => {
                            this.resetForm();
                            this.form1.props.action = 'admin/base/organization/sys/add';
                            this.form1.props.formType = 'add';
                            this.form1.props.title = '添加';
                            this.form1.props.show = true;

                            this.$set(this.form1.data, "parent_id", item._id);
                        }
                    },],
                stripe: true,
                size: "mini",
                border: true,
            },
            formDatas: {},
            // 表格相关结束 -----------------------------------------------------------
            // 表单相关开始-----------------------------------------------------------
            // 查询表单请求数据
            queryForm1: {
                // 查询表单数据源，可在此设置默认值
                formData: {

                },
                // 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
                columns: [

                ]
            },
            form1: {
                // 表单请求数据，此处可以设置默认值
                data: {
                    sort: 0,
                    enable: true,
                    url: ""
                },
                // 表单属性
                props: {
                    // 表单请求地址
                    action: "",
                    // 表单字段显示规则
                    columns: [
                        { key: "", title: "基础属性", type: "bar-title" },

                        { key: "name", title: "组织架构名称", type: "text", tips: "组织架构名称" },

                        { key: "sort", title: "排序值", type: "number", tips: "越小越显示在前面" },
                        {
                            key: "parent_id", title: "父级菜单", type: "tree-select", tips: "父级的_id",
                            action: "admin/base/organization/sys/getList",
                            props: { list: "rows", value: "_id", label: "name", children: "children" },
                        },
                    ],
                    // 表单对应的验证规则
                    rules: {

                        name: [
                            { required: true, message: '菜单名称不能为空', trigger: 'blur' },
                        ],
                        sort: [
                            { type: 'number', message: '排序值必须为数字' }
                        ]
                    },
                    // add 代表添加 update 代表修改
                    formType: '',
                    // 是否显示表单1 的弹窗
                    show: false,
                }
            },
            // 表单相关结束-----------------------------------------------------------
        };
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        that.init(options);
    },
    // 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
    onReady() { },
    // 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
    onShow() { },
    // 监听 - 页面每次【隐藏时】执行(如：返回)
    onHide() { },
    // 函数
    methods: {
        // 页面数据初始化函数
        init(options) {
            originalForms["form1"] = vk.pubfn.copyObject(that.form1);
        },
        // 页面跳转
        pageTo(path) {
            vk.navigateTo(path);
        },
        // 表单重置
        resetForm() {
            vk.pubfn.resetForm(originalForms, that);
        },
        // 搜索
        search() {
            that.$refs.table1.query();
        },
        // 刷新
        refresh() {
            that.$refs.table1.refresh();
        },
        // 获取当前选中的行的数据
        getCurrentRow(key) {
            return that.$refs.table1.getCurrentRow(key);
        },
        // 监听 - 行的选中高亮事件
        currentChange(val) {
            that.table1.selectItem = val;
        },
        // 当选择项发生变化时会触发该事件
        selectionChange(list) {
            that.table1.multipleSelection = list;
        },
        // 显示添加页面
        addBtn() {
            that.resetForm();
            that.form1.props.action = 'admin/base/organization/sys/add';
            that.form1.props.formType = 'add';
            that.form1.props.title = '添加';
            that.form1.props.show = true;
            let currentRow = that.getCurrentRow();
            if (currentRow && currentRow._id) {
                that.$set(that.form1.data, "parent_id", currentRow._id);

            }
        },
        // 显示修改页面
        updateBtn({ item }) {
            that.form1.props.action = 'admin/base/organization/sys/update';
            that.form1.props.formType = 'update';
            that.form1.props.title = '编辑';
            that.form1.props.show = true;
            that.form1.data = item;
        },
        formSuccess() {
            that.form1.props.show = false;
            // 下面的写法是为了部分修改完成后，减少一次再次请求数据库的查询
            if (that.form1.props.formType === "update") {
                let item = that.getCurrentRow(true);
                if (item.parent_id !== that.form1.data.parent_id) {
                    that.refresh();
                } else {
                    vk.pubfn.objectAssignForVue(item, that.form1.data, that);
                }
            } else {
                that.refresh();
            }
        },
        // 删除按钮
        deleteBtn({ item, deleteFn }) {
            deleteFn({
                action: "admin/base/organization/sys/delete",
                data: {
                    _id: item._id
                },
            });
        },
        // 修改排序值
        sortChange(sort, item) {
            vk.callFunction({
                url: 'admin/base/organization/sys/updateBase',
                data: {
                    _id: item._id,
                    sort: Number(sort)
                },
                success: (data) => {

                }
            });
        },
    },
    // 监听属性
    watch: {

    },
    // 计算属性
    computed: {

    }
};
</script>

<style lang="scss" scoped></style>
