'use strict';
module.exports = {
    /**
     * 检查已付款未设置展位状态订单
     * @url client/timedTask/pub/test 前端调用的url参数地址
     * data 请求参数
     * @param {String} params1  参数1
     */
    main: async (event) => {
        let { data = {}, userInfo, util, filterResponse, originalParam } = event;
        let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
        let { uid } = data;
        var $ = _.aggregate; // 聚合查询操作符
        let res = {
            code: 0,
            msg: ""
        };
        // 业务逻辑开始-----------------------------------------------------------

        await vk.baseDao.del({
            dbName: "aag-orders",
            whereJson: {
                _id: _.exists(true),
            }
        });


        // 业务逻辑结束-----------------------------------------------------------
        return res;
    }
}


