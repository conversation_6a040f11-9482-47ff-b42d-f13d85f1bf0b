<template>
    <view style="display: flex; flex-direction: column; justify-content: start; margin-top: 90px; margin-bottom: 80px">
        <view>
            <view style="display: flex; justify-content: space-around; width: 95%; font-family: 宋体; margin-top: 20px">
                <view style="width: 185px">
                    <img src="@/static/print-log.png" height="80px" width="170px" />
                </view>
                <view style="display: flex; flex-direction: column; justify-content: center">
                    <view>
                        <span style="font-size: 30px; color: black; display: ruby-text; font-weight: bold; line-height: 34px; flex-wrap: nowrap; width: 100%">深圳市金阅检测科技有限责任公司</span>
                    </view>
                    <view style="color: black; font-size: 23px; font-weight: bold; display: ruby-text">Shenzhen King Eye Testing Technology Co., Ltd</view>
                </view>
            </view>
        </view>

        <view style="width: 100%; padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px"></view>
        <view style="width: 100%; padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px"></view>
        <view>
            <table class="daying" style="border-spacing: 0; width: 100%; margin-top: 10px; border-collapse: collapse; font-family: 宋体; color: #000; font-size: 13px">
                <tr v-for="(detail, index) in data" :key="index" style="height: 26px">
                    <td align="center" width="4%" style="border: 1px solid #000; height: 15px">{{ index + pageSzie + 1 }}</td>
                    <td align="center" width="11%" style="border: 1px solid #000; height: 15px">
                        {{ detail.sampleno }}
                    </td>
                    <td align="center" width="11%" style="border: 1px solid #000; height: 15px">
                        {{ detail.samplename }}
                    </td>
                    <td align="center" colspan="2" width="16%" style="border: 1px solid #000; height: 15px">
                        {{ detail.detection_category }}
                    </td>
                    <td align="center" width="14%" style="border: 1px solid #000; height: 15px">
                        {{ detail.detection_standard }}
                    </td>
                    <td align="center" width="11%" style="border: 1px solid #000; height: 15px">
                        {{ detail.detection_include }}
                    </td>
                    <td align="center" width="8%" style="border: 1px solid #000; height: 15px" :style="{ 'font-size': '14px' }">
                        {{ detail.result }}
                    </td>
                        <td align="center" width="10%" style="border: 1px solid #000; height: 15px" :style="{ 'font-size': '14px' }">
                        {{ detail.remark }}
                    </td>
                </tr>
            </table>
        </view>
        <view v-if="!isLast" style="width: 95%; font-size: 16px; font-weight: bold; margin-bottom: 10%; position: relative" :style="{ 'margin-top': '2px' }">
            <view style="z-index: 999; position: absolute; transform: translateX(-100%) translateY(-90px); margin-left: 100%; background-color: transparent">
                <img src="@/static/gz.png" width="150px;" height="150px;" />
            </view>
        </view>
        <print_footer v-if="!isLast" :pageIndex="pageIndex"></print_footer>
    </view>
</template>

<script>
import { remove, concat, find, includes, unionBy } from 'lodash';
import print_footer from './print_footer.vue';
var that; // 当前页面对象
var vk = uni.vk; // vk实例
// 表单初始化数据

export default {
    props: {
        data: {
            Type: Array,
            default: function () {
                return [];
            }
        },
        isLast: {
   Type:Boolean,
            default: function () {
                return false;
            }
        },
        pageSzie: {
            Type: Number,
            default: function () {
                return 10;
            }
        },
        pageIndex: {
            Type: String,
            default: function () {
                return '';
            }
        },
        data() {
            // 页面数据变量
            return {};
        },
        // 监听 - 页面每次【加载时】执行(如：前进)
        onLoad(options = {}) {
            that = this;
            vk = that.vk;
            that.options = options;
            that.init(options);
        },
        onUnload() {
            // 返回false阻止页面被销毁
            return false;
        },
        // 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
        onReady() {},
        // 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
        onShow() {},
        // 监听 - 页面每次【隐藏时】执行(如：返回)
        onHide() {},
        // 函数
        methods: {},
        // 监听属性
        watch: {},
        // 过滤器
        filters: {}
    },
    components: {
        print_footer
    }
};
</script>

<style lang="scss" scoped>
.page-body {
}
</style>
