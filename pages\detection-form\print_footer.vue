<template>
    <view style="display: flex; flex-direction: column; justify-content: start; ">
        <view style="display: flex; justify-content: center; text-align: center; font-size: 10px;">{{ pageIndex }}
        </view>
        <view
            style="padding: 0px; background-color: black; margin-top: 3px; margin-bottom: 3px; font-weight: bold; border-bottom: 1px solid rgb(0, 0, 0);">
        </view>
        <view style="font-size: 9px;">
            Hotline 0755-28380866，深圳市金阅检测科技有限责任公司 地址：深圳市大鹏新区大鹏街道布新社区布新村工业大道 2 号 C401
            声明：未经本公司授权，本报告全部或部分复制、私自转让、盗用、冒用、涂改或以其他形式篡改无效；如果对检测结果有异议，
            请在检测报告完成之日起 4 小时内向我司书面提出复检申请（附上检测报告原件），逾期不予受理；送样委托检测结果仅对来样负
            责
        </view>
    </view>
</template>

<script>
import { remove, concat, find, includes, unionBy } from 'lodash'
var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
				// 表单初始化数据

export default {
    props: {
        pageIndex: {
            Type: String,
            default: function () {
                return '第一页';
            }
        },
    },
    data() {
        // 页面数据变量
        return{}
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        that.init(options);
    },
    onUnload() {
        // 返回false阻止页面被销毁
        return false;
    },
    // 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
    onReady() { },
    // 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
    onShow() { },
    // 监听 - 页面每次【隐藏时】执行(如：返回)
    onHide() { },
    // 函数
    methods: {},
    // 监听属性
    watch: {

    },
    // 过滤器
    filters: {

    },
    // 计算属性
    computed: {

    }
};
</script>

<style lang="scss" scoped>
.page-body {}
</style>
