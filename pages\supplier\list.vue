<template>
	<view class="page-body">
		<!-- 页面内容开始 -->

		<!-- 表格搜索组件开始 -->
		<vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" size="mini"
			@search="search"></vk-data-table-query>
		<!-- 表格搜索组件结束 -->

		<!-- 自定义按钮区域开始 -->
		<view>
			<el-row>
				<el-button type="success" size="mini" icon="el-icon-circle-plus-outline" @click="addBtn">添加</el-button>
				<!-- 批量操作 -->
				<el-dropdown v-if="table1.multipleSelection" :split-button="false" trigger="click" @command="batchBtn">
					<el-button type="danger" size="mini" style="margin-left: 20rpx;"
						:disabled="table1.multipleSelection.length === 0">
						批量操作<i class="el-icon-arrow-down el-icon--right"></i>
					</el-button>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item :command="1">批量操作1</el-dropdown-item>
						<el-dropdown-item :command="2">批量操作2</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</el-row>
		</view>
		<!-- 自定义按钮区域结束 -->

		<!-- 表格组件开始 -->
		<vk-data-table ref="table1" :rowNo="true" :border="true" :action="table1.action" :columns="table1.columns"
			:query-form-param="queryForm1" size="mini" :right-btns="['detail_auto', 'update', 'delete']"
			:selection="true" :pagination="true" @update="updateBtn" @delete="deleteBtn" @current-change="currentChange"
			@selection-change="selectionChange"></vk-data-table>
		<!-- 表格组件结束 -->

		<!-- 添加或编辑的弹窗开始 -->
		<vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="900px" mode="form"
			:close-on-click-modal="false">
			<vk-data-form v-model="form1.data" :rules="form1.props.rules" :action="form1.props.action"
				:form-type="form1.props.formType" :columns='form1.props.columns' label-width="120px"
				@success="form1.props.show = false; refresh();" :inline="true" :columnsNumber="2"></vk-data-form>
		</vk-data-dialog>
		<!-- 添加或编辑的弹窗结束 -->

		<!-- 页面内容结束 -->
	</view>
</template>

<script>
var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
var originalForms = {};						// 表单初始化数据

export default {
	data() {
		// 页面数据变量
		return {
			// 页面是否请求中或加载中
			loading: false,
			supplier_types: [],
			delivery_companys: [],
			// init请求返回的数据
			data: {

			},
			// 表格相关开始 -----------------------------------------------------------
			table1: {
				// 表格数据请求地址
				action: "admin/base/supplier/sys/getList",
				// 表格字段显示规则
				columns: [
					// { key:"_id", title:"id", type:"text", width:220 },

					{ key: "name", title: "供应商名称", type: "text", width: 400, sortable: "custom" },
					{ key: "supplier_type", title: "供应商类型", type: "text", width: 200, sortable: "custom", formatter: this.supplier_type_fomattr },
					{ key: "delivery_company", title: "所属配送公司", type: "text", width: 250, sortable: "custom", formatter: this.delivery_company_fomattr },
					{ key: "thumb", title: "附件", type: "image", show: ["detail"] },
					{ key: "sort", title: "排序", type: "number", width: 100, sortable: "custom" },
					// { key:"_add_time", title:"添加时间", type:"time", width:160, sortable:"custom"  },
					// { key:"_add_time", title:"距离现在", type:"dateDiff", width:120 },
				],
				// 多选框选中的值
				multipleSelection: [],
				// 当前高亮的记录
				selectItem: ""
			},
			// 表格相关结束 -----------------------------------------------------------
			// 表单相关开始 -----------------------------------------------------------
			// 查询表单请求数据
			queryForm1: {
				// 查询表单数据源，可在此设置默认值
				formData: {

				},
				// 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
				columns: [
					{ key: "name", title: "供应商名称", type: "text", width: 160, mode: "%%" },

				]
			},
			form1: {
				// 表单请求数据，此处可以设置默认值
				data: {
				},
				// 表单属性
				props: {
					// 表单请求地址
					action: "",
					// 表单字段显示规则
					columns: [

						{ key: "name", title: "供应商名称", type: "text", width: 400, sortable: "custom", oneLine: true },

						{
							key: "supplier_type", title: "供应商类型", type: "remote-select", placeholder: "请选择供应商类型",
							action: "admin/base/goods_type/sys/getList",
							props: { list: "rows", value: "_id", label: "name" },
							showAll: true,
							actionData: {
								pageSize: -1
							}, width: 250,
							oneLine: true
						},
						{
							key: "delivery_company", title: "所属配送公司", type: "select", placeholder: "请选择所属配送公司",
						data:[],
							props: { list: "rows", value: "_id", label: "name" },
					

							watch: ({ value, formData, column, index, option, $set }) => {

							
								$set("delivery_company_name", option.name);
							
							},
							width: 500,
							oneLine: true
						},

						{ key: "thumb", title: "附件", type: "image", limit: 3, oneLine: true },
						{ key: "sort", title: "排序", type: "number", placeholder: "请输入排序权重", width: 200, oneLine: true },
					],
					// 表单验证规则
					rules: {
						name: [
							// 必填
							{ required: true, message: "供应商名称不能为空", trigger: ['blur', 'change'] }
						],
						supplier_type: [
							// 必填
							{ required: true, message: "供应商类型不能为空", trigger: ['blur', 'change'] }
						],
						delivery_company: [
							// 必填
							{ required: true, message: "所属配送公司不能为空", trigger: ['blur', 'change'] }
						],
						// sort: [
						// 	// 必填
						// 	{ required: true, message: "排序不能为空", trigger: ['blur', 'change'] }
						// ],
					},
					// add 代表添加 update 代表修改
					formType: "",
					// 是否显示表单的弹窗
					show: false
				}
			},
			// 其他弹窗表单
			formDatas: {},
			// 表单相关结束 -----------------------------------------------------------
		};
	},
	// 监听 - 页面每次【加载时】执行(如：前进)
	onLoad(options = {}) {
		that = this;
		vk = that.vk;
		that.options = options;
		that.init(options);
	},
	onUnload() {
		// 返回false阻止页面被销毁
		return false;
	},
	// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
	onReady() { },
	// 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
	onShow() { },
	// 监听 - 页面每次【隐藏时】执行(如：返回)
	onHide() { },
	// 函数
	methods: {
		// 页面数据初始化函数
		init(options) {
			originalForms["form1"] = vk.pubfn.copyObject(that.form1);
			vk.callFunction({
				url: 'admin/base/goods_type/sys/getList',
				data: {
					page: 1,
					pageSize: -1
				},
				success: (data) => {
					this.supplier_types = data.rows;
				}
			});
			let delivery_company =undefined;
            if(this.$hasRole('admin')){
            }else if(this.$hasRole('ROLE_PSGS')){
                
                delivery_company=vk.getVuex('$user.userInfo').nickname;
            }else{

                return;
            }    

			vk.callFunction({
				url: 'admin/base/client/sys/getList',
				data: {
					columns:[{
                        key: "name",
                        mode: "=",
                        title: "配送公司名称",
                        type: "text",
                    }                 
                    ],
                    formData:{
                        name:delivery_company,
                    },
					page: 1,
					pageSize: -1
				},
				success: (data) => {
					this.delivery_companys = data.rows;
				}
			});
		},
		supplier_type_fomattr(val, row, column, index) {
			// console.log(this.categories.find(x=>x._id==val))
			return this.supplier_types.find(x => x._id == val).name;

		},
		delivery_company_fomattr(val, row, column, index) {
			// console.log(this.categories.find(x=>x._id==val))
			return this.delivery_companys.find(x => x._id == val).name;

		},
		// 页面跳转
		pageTo(path) {
			vk.navigateTo(path);
		},
		// 表单重置
		resetForm() {
			vk.pubfn.resetForm(originalForms, that);
		},
		// 搜索
		search() {
			that.$refs.table1.search();
		},
		// 刷新
		refresh() {
			that.$refs.table1.refresh();
		},
		// 获取当前选中的行的数据
		getCurrentRow() {
			return that.$refs.table1.getCurrentRow();
		},
		// 监听 - 行的选中高亮事件
		currentChange(val) {
			that.table1.selectItem = val;
		},
		// 当选择项发生变化时会触发该事件
		selectionChange(list) {
			that.table1.multipleSelection = list;
		},
		// 显示添加页面
		addBtn() {
			that.resetForm();
			that.form1.props.action = 'admin/base/supplier/sys/add';
			that.form1.props.formType = 'add';
			that.form1.props.title = '添加';
			that.form1.props.show = true;
		},
		// 显示修改页面
		updateBtn({ item }) {
			that.form1.props.action = 'admin/base/supplier/sys/update';
			that.form1.props.formType = 'update';
			that.form1.props.title = '编辑';
			that.form1.props.show = true;
			that.form1.data = item;
		},
		// 删除按钮
		deleteBtn({ item, deleteFn }) {
			deleteFn({
				action: "admin/base/supplier/sys/delete",
				data: {
					_id: item._id
				},
			});
		},
		// 监听 - 批量操作的按钮点击事件
		batchBtn(index) {
			switch (index) {
				case 1: vk.toast("批量操作按钮1"); break;
				case 2: vk.toast("批量操作按钮2"); break;
				default: break;
			}
		}
	},
	// 监听属性
	watch: {
		delivery_companys(newval){
			let _obj = that.form1.props.columns.find(x => x.key == 'delivery_company')
			that.$set(_obj, 'data', newval)
		}
	},
	// 过滤器
	filters: {

	},
	// 计算属性
	computed: {

	}
};
</script>

<style lang="scss" scoped>
.page-body {}
</style>
