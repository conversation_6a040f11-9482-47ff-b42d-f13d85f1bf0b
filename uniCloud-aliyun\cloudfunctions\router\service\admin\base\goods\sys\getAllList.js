module.exports = {
    /**
     * 查询多条记录 分页
     * @url admin/kong/sys/getList 前端调用的url参数地址
     * data 请求参数 说明
     * @param {Number}         pageIndex 当前页码
     * @param {Number}         pageSize  每页显示数量
     * @param {Array<Object>}  sortRule  排序规则
     * @param {object}         formData  查询条件数据源
     * @param {Array<Object>}  columns   查询条件规则
     * @param {String}         name      搜索关键词 (新增参数)
     * res 返回参数说明
     * @param {Number}         code      错误码，0表示成功
     * @param {String}         msg       详细信息
     */
    main: async event => {
        let { data = {}, userInfo, util, filterResponse, originalParam } = event;
        let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
        let { uid, searchvalue, exactMatch } = data; // 从请求参数中提取搜索关键词和精确匹配标志
        let res = { code: 0, msg: '' };
        // 业务逻辑开始-----------------------------------------------------------
        let dbName = 'tm-goods';
        let _whereJson = {};

        // 如果提供了搜索关键词，添加搜索条件
        if (searchvalue) {
            if (exactMatch) {
                // 精确匹配（不区分大小写）
                _whereJson = {
                    name: new RegExp(`^${searchvalue}$`, 'i') // '^'表示开始，'$'表示结束，'i'表示不区分大小写
                };
            } else {
                // 模糊匹配
                _whereJson = {
                    name: new RegExp(searchvalue, 'i') // 'i'表示不区分大小写
                };
            }
        }

        res = await vk.baseDao.select({
            dbName,
            pageIndex: data.pageIndex || 1,
            pageSize: data.pageSize || 9999,
            whereJson: _whereJson,
            sortArr: [{ name: 'name', type: 'desc' }]
        });

        return res;
    }
};
