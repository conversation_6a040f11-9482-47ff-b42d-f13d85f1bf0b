<template>
    <vk-data-dialog v-model="value.show" :title="page.title" :top="page.top" :width="page.width" mode="form"
        max-height="800px" width="860px" @open="onOpen" @closed="onClose" :close-on-click-modal="true">
        <div id="tableId-0" ref="printContent">
            <div v-for="(v, i) in value.printData" :key="i" style="page-break-after: always">
                <div style="width: 850px; align-content: center">
                    <div style="z-index: -1; display: flex; -ms-flex-align: center; align-items: center; -ms-flex-pack: center;justify-content: start; -ms-flex-direction: column; flex-direction: column"
                        :style="{ height: getPageHeight(v) + 'px' }">
                        <!--!封面页开始-->
                        <!--!封面页结束-->
                        <div>
                            <div style="display: flex; width: 100%; font-family: 宋体; margin-top: 20px">
                                <div style="width: 185px">
                                    <img src="@/static/print-log.png" height="80px" width="170px" />
                                </div>
                                <div style="display: flex; flex-direction: column; justify-content: center">
                                    <div>
                                        <span
                                            style="font-size: 30px; color: black; display: ruby-text; font-weight: bold; line-height: 34px; flex-wrap: nowrap; width: 100%">深圳市金阅检测科技有限责任公司</span>
                                    </div>
                                    <div style="color: black; font-size: 23px; font-weight: bold; display: ruby-text">
                                        Shenzhen King Eye Testing Technology Co., Ltd</div>
                                </div>
                            </div>
                        </div>
                        <div
                            style="width: 95%; padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px">
                        </div>
                        <div
                            style="width: 95%; padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px">
                        </div>
                        <div
                            style="position: relative;display: flex;justify-content: center; width: 95%; font-family: 宋体">
                            <div
                                style="font-size: 30px !important; font-weight: bold; font-weight: bold; color: black; margin-top: 20px; text-align: center">
                                检 测 报 告</div>
                        </div>
                        <div style="width: 95%">
                            <div style="margin-bottom: 5px; display: flex; justify-content: space-between">
                                <div style="display: flex; justify-content: start">
                                    <span
                                        style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">报告编号：</span>
                                    <span style="font-size: 16px; font-family: 宋体; color: black">{{ v.no || '' }}</span>
                                </div>

                                <div style="width: 120px">
                                    <span style="display: ruby-text">
                                        {{ `第${1}页 / 共${getPageCount(v)}页` }}
                                    </span>
                                </div>
                            </div>
                            <div>
                                <table class="daying"
                                    style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr>
                                        <td colspan="4" align="center"
                                            style="height: 30px; border: 1px solid #000; height: 15px"><span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">客户信息及检测信息</span>
                                        </td>
                                        <td rowspan="6" class="qrcode-cell" style="border: 1px solid #000; width: 20%">
                                            <div
                                                style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%">
                                                <vue-qr :logoSrc="logourl" :margin="2"
                                                    :text="`https://static-host-uebpmzce-web.gzg.sealos.run/#/pages/qr_info/aq?no=${v.no}`"
                                                    :size="120"></vue-qr>

                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center"
                                            style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">委托单位名称</span>
                                        </td>
                                        <td align="center" colspan="3"
                                            style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.client_name }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">委托单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.address }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受
                                                检 单 位</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.submitUser }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受检单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ (v.submituser_address || v.addressps) ?
                                                (v.submituser_address || v.addressps) : (v.client_name === v.submitUser
                                                ? v.address : '') }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center"
                                            style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">接
                                                收 日 期</span>
                                        </td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat(v.detect_time,
                                                'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样
                                                品 状 态</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">鲜样正常</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center"
                                            style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">验
                                                证 日 期</span>
                                        </td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat(v.detect_time,
                                                'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检
                                                测 类 型</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">委托检测</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 12px">扫描二维码辨别报告真伪</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div
                            style="align-items: center; font-size: 18px; font-weight: bold; font-weight: bold; margin-top: 6px; margin-bottom: 6px; font-family: 宋体; color: black">
                            检 测 结 果</div>
                        <div style="width: 95%">
                            <div>
                                <table class="daying"
                                    style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr style="height: 30px">
                                        <td align="center" width="5%" style="border: 1px solid #000; height: 15px"><span
                                                style="font-weight: bold; font-size: 16px; font-family: 黑体">序号</span>
                                        </td>
                                        <td align="center" width="12%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品编号</span>
                                        </td>
                                        <td align="center" width="12%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品名称</span>
                                        </td>
                                        <td align="center" colspan="2" width="16%"
                                            style="border: 1px solid #000; height: 15px"><span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测项目</span>
                                        </td>
                                        <td align="center" width="12%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">单位</span>
                                        </td>

                                        <td align="center" width="12%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检出限</span>
                                        </td>

                                        <td align="center" width="11%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">单项判定</span>
                                        </td>
                                    </tr>

                                    <!-- 动态行高和字体大小 -->
                                    <tr v-for="(detail, index) in getsubList(v.sample_list, 1)" :key="index"
                                        :style="{ height: calcRowHeight(v.sample_list.length) + 'px', fontSize: calcFontSize(v.sample_list.length) + 'px' }">
                                        <td align="center" style="border: 1px solid #000; height: 15px">{{ index + 1 }}
                                        </td>
                                        <td align="center" v-if="index == 0" :rowspan="v.sample_list.length"
                                            style="border: 1px solid #000; height: 15px">{{
                                                detail.sampleno }}</td>
                                        <td align="center" v-if="index == 0" :rowspan="v.sample_list.length"
                                            style="border: 1px solid #000; height: 15px">{{
                                                detail.samplename }}</td>
                                        <td align="center" colspan="2" style="border: 1px solid #000; height: 15px">{{
                                            detail.detection_category }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            {{ detail.unit || 'μg/kg' }}
                                        </td>
                                        <!-- <td align="center" style="border: 1px solid #000; height: 15px">{{
                                            detail.detection_standard }}</td> -->
                                        <td align="center" style="border: 1px solid #000; height: 15px">{{
                                            detail.detection_include }}</td>

                                        <td align="center" style="border: 1px solid #000; height: 15px"
                                            :style="{ 'font-size': '14px' }">
                                            {{ detail.result }}
                                        </td>
                                    </tr>
                                </table>

                                <div v-if="v.sample_list.length > pageSzie"
                                    style="width: 100%; font-size: 16px; font-weight: bold; margin-top: 3%; margin-bottom: 10%; position: relative">
                                    <div
                                        style="z-index: 999; position: absolute; transform: translateX(-140%) translateY(-200px); margin-left: 100%; background-color: transparent">
                                        <img src="@/static/gz.png" width="150px;" height="150px;" />
                                    </div>
                                </div>
                                <print_footer v-if="v.sample_list.length > pageSzie" :pageIndex="`第一页`"></print_footer>
                                <print_content v-if="v.sample_list.length > pageSzie" :pageSzie="pageSzie"
                                    :data="getsubList(v.sample_list, 2)"
                                    :isLast="v.sample_list.length > pageSzie && v.sample_list.length < pageSzie2"
                                    :pageIndex="`第二页`" />
                                <print_content v-if="v.sample_list.length > pageSzie2" :pageSzie="pageSzie2"
                                    :data="getsubList(v.sample_list, 3)"
                                    :isLast="v.sample_list.length > pageSzie2 && v.sample_list.length < pageSzie3"
                                    :pageIndex="`第三页`" />
                                <print_content v-if="v.sample_list.length > pageSzie3" :pageSzie="pageSzie3"
                                    :data="getsubList(v.sample_list, 4)"
                                    :isLast="v.sample_list.length > pageSzie3 && v.sample_list.length < pageSzie4"
                                    :pageIndex="`第四页`" />
                                <print_content v-if="v.sample_list.length > pageSzie4" :pageSzie="pageSzie4"
                                    :data="getsubList(v.sample_list, 5)"
                                    :isLast="v.sample_list.length > pageSzie4 && v.sample_list.length < pageSzie5"
                                    :pageIndex="`第五页`" />
                                <table
                                    style="color: black;width: 100%;  font-size: 13px; font-family: 黑体;border-collapse: collapse;">
                                    <tr>
                                        <td width="12%"
                                            style="border: 1px solid #000; height: 20px;padding-left: 8px;padding-right: 8px;">
                                            检测方法:</td>
                                        <td width="88%"
                                            style="border: 1px solid #000; height: 20px;padding-left: 8px;padding-right: 8px;">
                                            {{uniq(v.sample_list.map(x => x.detection_standard)).join('/')}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td
                                            style="border: 1px solid #000; height: 15px;padding-left: 8px;padding-right: 8px;">
                                            判定依据:</td>
                                        <td
                                            style="border: 1px solid #000; height: 15px;padding-left: 8px;padding-right: 8px;">
                                            KJ 201701、KJ201705、KJ201905、KJ201906 食品中磺胺类的快速检测
                                        </td>

                                    </tr>
                                    <tr>
                                        <td colspan="2" style="border: 1px solid #000; padding: 8px;">
                                            {{
                                                v.remark ||`磺胺类:磺胺嘧啶 20μg/kg 磺胺甲基嘧啶 20μg/kg 磺胺索嘧啶 20μg/kg 磺胺对甲嘧啶20μg/kg 磺胺氨吡嗪
                                            30μg/kg
                                            磺胺二甲氧密啶20μg/kg 磺胺间甲氧嘧啶20μg/kg 磺胺多辛20μg/kg 磺胺甲噻二唑20μg/kg 磺胺氯哒嗪30μg/kg
                                            磺胺甲氧哒嗪40μg/kg 磺胺二甲基嘧啶70μg/kg 磺胺甲噁唑100μg/kg 磺胶苯酰40μg/kg 磺胶喹恶林80μg/kg
                                            磺胶异噁唑100μg/kg`
                                            }}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div
                                style="text-align: left; padding: 5px 5px; padding-left: 10px;font-family: 黑体;color: #000; font-size: 13px;border: 1px solid #000; border-top: none;">
                                注：请客户仔细阅读检测报告的申明</br>
                                1、报告无快检专用章无效；报告无检测人、审核人签名无效，报告经涂改、增删无效；</br>
                                2、以上样品信息均由客户提供，本司不承担其信息准确性的责任；</br>
                                3. 本报告只对送检样品检测结果负责。以上检测结果为快速检测定性检测结果，不具备法律效力，仅供客户参考。</br>
                                4、委托单位对本检测报告有异议，请在收到报告之日或指定领取报告之日起，三个工作日内提出申诉，逾期不子受理。</div>
                        </div>

                        <!--?印章-->
                        <div style="background-color: aquamarine; width: 95%; font-size: 16px; font-weight: bold; margin-bottom: 10%; position: relative"
                            :style="{ 'margin-top': v.sample_list.length > pageSzie ? '-2px' : '2px' }">
                            <div
                                style="z-index: 999; position: absolute; transform: translateX(-140%) translateY(-50%); margin-left: 100%; background-color: transparent">
                                <img src="@/static/gz.png" width="150px;" height="150px;" />
                            </div>
                            <div
                                style="margin-left: 100%; z-index: 2; white-space: nowrap; position: absolute; transform: translateX(-100%); font-family: 黑体">
                                此处未盖本公司快检专用章，则本报告无效。</div>
                        </div>
                        <!--?印章-->
                        <div
                            style="display: flex; flex-direction: row; justify-content: space-between; width: 95%; z-index: 1">
                            <div
                                style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                                <div>检测人:</div>
                                <div style="position: absolute; left: 28%; top: -80%">
                                    <span>
                                        <img v-if="$fn.isNotNull(v.detection_user_info.sign_image)" width="100px"
                                            height="40px" :src="v.detection_user_info.sign_image" />
                                    </span>
                                </div>
                            </div>
                            <div
                                style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                                <div>审核人:</div>
                                <div style="position: absolute; left: 28%; top: -60%">
                                    <img v-if="$fn.isNotNull(v.detection_user_info.reviewer_sign_image)"
                                         :src="v.detection_user_info.reviewer_sign_image"
                                         width="100px" height="40px" />
                                    <img v-else src="@/static/shr.png" width="100px" height="40px" />
                                </div>
                            </div>
                            <div style="width: 34%; font-size: 16px; font-weight: bold; font-family: 黑体">
                                <span>签发日期:</span>
                                <span>{{ vk.pubfn.timeFormat(v.detect_time, 'yyyy年MM月dd日') }}</span>
                            </div>
                        </div>

                        <div style="margin-top: auto; width: 95%; padding-bottom: 12px">

                            <div
                                style="padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px">
                            </div>
                            <div
                                style="padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px">
                            </div>
                            <div style="display: flex; align-content: space-between; color: #606266; margin-top: 10px">
                                <div style="font-size: 12px; font-family: 黑体; display: ruby-text">Hotline 0755-28380866
                                    深圳市金阅检测科技有限责任公司</div>
                                <div
                                    style="margin-left: 22%; font-size: 12px;text-align: end; font-family: 黑体; align-content: flex-end; color: #606266">
                                    地址：深圳市大鹏新区大鹏街道布新社区布新村工业大道2号C401</div>
                            </div>
                            <div
                                style="margin-top: 20px;text-align:center ;width: 100%; margin-bottom: 20px; text-align: center; font-size: 18px">
                                ***报告完结***</div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template v-slot:footer="{ close }">
            <!--这里是底部插槽-->
            <el-button @click="advancedPrint">打 印</el-button>
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="close">确 定</el-button>
        </template>
    </vk-data-dialog>
</template>

<script>
import VueQr from 'vue-qr';
import Print from 'print-js';
import print_content from './print_content.vue';
import print_footer from './print_footer.vue';
import printbase from '@/mixins/printbase.js';
import { slice, uniq } from 'lodash';
var that; // 当前页面对象
var vk = uni.vk; // vk实例
export default {
    components: {
        VueQr,
        print_footer,
        print_content
    }, mixins: [printbase],
    props: {
        value: {
            Type: Object,
            default: function () {
                return {
                    show: false,
                    printData: []
                };
            }
        }
    },
    data: function () {
        // 组件创建时,进行数据初始化
        return {
            page: {
                title: '报告打印',
                submitText: '确定',
                cancelText: '关闭',
                showCancel: true,
                top: '3vh',
                width: '806px'
            },
            form1: {},
            logourl: require('@/static/logo.png'),
            pageSzie: 22,
            pageSzie2: 53,
            pageSzie3: 84,
            pageSzie4: 105,
            pageSzie4: 126,
            onePageHeight: 1200
        };
    },
    mounted() {
        that = this;

        that.init();
    },
    methods: {
        // 初始化
        init() {
            let { value } = that;

            //that.$emit("input", value);
        },
        handleImport() {
            this.$refs.file.click();
        },
        // 监听 - 页面打开
        onOpen() {
            that = this;
            let { item = {} } = that.value;
            that.form1.props.show = true;
        },
        // 监听 - 页面关闭
        onClose() { },
        // 监听 - 提交成功后
        onFormSuccess() {
            that.value.show = false; // 关闭页面
            that.$emit('success');
        },

        print2() {
            printJS({
                printable: `tableId-0`, // 'printFrom', // 标签元素id
                type: 'html',
                style: '@page {margin:1 1mm;font-size:7px} .daying {border-spacing:0;width:100%;border-collapse:collapse;font-family:宋体;color:#000} .daying td {border:1pxsolid#000;height:15px} .daying td label {font-size:14px} .daying td span{font-size:16px}', // 可选-打印时去掉眉页眉尾
                scanStyles: false,
                ignoreElements: [], // ['no-print']
                properties: null
            });
            let _updateIds = this.value.printData.map(x => x._id);
            vk.callFunction({
                url: 'admin/testing/detection-form/sys/update_print_count',
                data: {
                    _ids: _updateIds
                },
                success: data => { }
            });
        },
        // 根据 sample_list 长度计算行高
        calcRowHeight(length) {
            // return length < 20 ? 30 : 20;
            return 30;
        },
        // 根据 sample_list 长度计算字体大小
        calcFontSize(length) {
            return 12;
            //  return length < 20 ? 14 : 10;
        },
        getsubList(list, index) {
            if (index == 1) {
                return list.slice(0, this.pageSzie);
            } else if (index == 2) {
                return slice(list, this.pageSzie, this.pageSzie2);
            } else if (index == 3) {
                return slice(list, this.pageSzie2, this.pageSzie3);
            } else if (index == 4) {
                return slice(list, this.pageSzie3, this.pageSzie4);
            }
            else if (index == 5) {
                return slice(list, this.pageSzie4, list.length);
            }
        },
        getSampleSatus(data) {
            let _result = '鲜样正常';

            if (vk.pubfn.isNotNull(data.sample_list)) {
                data.sample_list.forEach(element => {
                    if (vk.pubfn.isNotNull(element.yxresult)) {
                        if (element.yxresult != '阴性') {
                            _result = element.yxresult;
                            return _result;
                        }
                    }
                });
            }
            return _result;
        },
        getPageHeight(v) {
            return 1200;
            let _height = this.onePageHeight;
            if (v.sample_list.length < this.pageSzie) {
                return _height;
            } else {
                _height = this.onePageHeight + this.onePageHeight * Math.ceil((v.sample_list.length - this.pageSzie) / (this.pageSzie * 1.5));
            }
            //  console.log(_height);
            return _height;
        },
        getPageCount(v) {
            if (v.sample_list.length < this.pageSzie) {
                return 1;
            } else {
                return 2 + Math.ceil((v.sample_list.length - this.pageSzie) / (this.pageSzie * 1.5));
            }
        },
        uniq(list) {
            return uniq(list)
        }

    },
    // 监听属性
    watch: {

    },
    // 计算属性
    computed: {
        first15SampleList() {
            return this.sample_list.slice(0, 15);
        },
        // 使用计算属性来获取剩余的样本
        remainingSampleList() {
            return this.sample_list.slice(15);
        }
    }
};
</script>

<style lang="scss" scoped>
// .daying {
//     border-spacing: 0;
//     width: 100%;
//     border-collapse: collapse;
//     font-family: 宋体;
//     color: #000
// }

// .daying td {
//     border: 1px solid #000;
//     height: 15px
// }

// .daying td label {
//     font-size: 14px
// }

// .daying td span {
//     font-size: 16px
// }

.page-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
}

.qrcode-cell {
    width: 150px;
    text-align: center;
    vertical-align: middle;
}

.qrcode {
    width: 120px;
    height: 120px;
}
</style>
