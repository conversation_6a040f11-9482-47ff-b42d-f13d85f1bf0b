<template>
    <view class="page-body">
        <!-- 页面内容开始 -->

        <!-- 表格搜索组件开始 -->
        <vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" size="mini"
            @search="search"></vk-data-table-query>
        <!-- 表格搜索组件结束 -->

        <!-- 自定义按钮区域开始 -->
        <view>
            <el-row> </el-row>
        </view>
        <!-- 自定义按钮区域结束 -->

        <!-- 表格组件开始 -->
        <vk-data-table ref="table1" :rowNo="true" :border="true" :action="table1.action" :columns="table1.columns"
            :query-form-param="queryForm1" :custom-right-btns="table1.customRightBtns" size="mini" :show-summary="true"
            :total-option="[
            { key: 'count', 'unit': '次', type: 'number', precision: 2 },
            { key: 'price', 'unit': '元', type: 'number', precision: 2 }
        ]" @current-change="currentChange"></vk-data-table>
        <!-- 表格组件结束 -->
        <vk-data-dialog v-model="detailshow" :title="`${username}检验记录`" width="1150px" mode="default"
            :close-on-click-modal="true" style="padding: 5px" max-height="700">
            <view><el-button @click="exportexl">导出</el-button></view>
            <view style="padding: 5px;">
                <vxe-table ref="xTable" height="700" :loading="loading" keep-source border resizable show-overflow
                    size="small" :data="userdata" show-footer :footer-method="footerDetailMethod">
                    <vxe-table-column fixed="left" type="seq" width="80"></vxe-table-column>
                    <vxe-table-column width="80" field="sampleno" title="no"></vxe-table-column>
                    <vxe-table-column field="detection_category" title="检测项目名称"></vxe-table-column>
                    <vxe-table-column field="samplename" title="样品名称"></vxe-table-column>
                    <vxe-table-column field="price" width="80" title="费用(元)"></vxe-table-column>
                    <vxe-table-column field="client_name" title="送检单位"></vxe-table-column>
                    <vxe-table-column field="_add_time" title="录入时间" formatter="formatTime"></vxe-table-column>
                </vxe-table>
            </view>
        </vk-data-dialog>
    </view>
</template>

<script>
import { add } from 'lodash'
var that; // 当前页面对象
var vk = uni.vk; // vk实例
var originalForms = {}; // 表单初始化数据

export default {
    data() {
        // 页面数据变量
        return {
            // 页面是否请求中或加载中
            loading: false,
            detailshow: false,
            username: '',
            // init请求返回的数据
            data: {},
            userdata: [],
            clients: [],//委托单位列表
            // 表格相关开始 -----------------------------------------------------------
            table1: {
                // 表格数据请求地址
                action: "admin/testing/comprehensive-query/sys/groupqueryby_user",
                // 表格字段显示规则
                columns: [
                    // { key:"_id", title:"id", type:"text", width:220 },

                    { key: "detection_user", title: "检测人", type: "text", width: 500 },
                    { key: "count", title: "检测样品数量", type: "number", width: 200 },
                    { key: "price", title: "检测总费用", type: "number", width: 200 },
                ],
                // 多选框选中的值
                multipleSelection: [],
                // 当前高亮的记录
                selectItem: "",
                customRightBtns: [
                    {
                        title: "详情",
                        type: "primary",

                        onClick: async (item) => {
                            that.username = item.detection_user
                            that.detailshow = true;
                            that.$set(that, 'userdata', [])
                            that.loading = true;
                            await vk.callFunction({
                                url: 'admin/testing/comprehensive-query/sys/querybyuser',
                                data: {

                                    detection_user: item.detection_user,
                                    ...that.queryForm1.formData,

                                },
                                success: (data) => {
                                    that.$set(that, 'userdata', data.rows)
                                    // that.userdata = data.rows;

                                }
                            });
                            that.loading = false;

                        },
                    },
                ],
            },
            table2: {
                // 表格数据请求地址
                action: "admin/testing/comprehensive-query/sys/querybyuser",
                // 表格字段显示规则
                columns: [
                    // { key:"_id", title:"id", type:"text", width:220 },

                    { key: "sampleno", title: "no.", type: "text", width: 100 },
                    { key: "detection_category", title: "检测项目名称", type: "text", width: 180 },

                    {
                        key: "client_name",
                        title: "送检单位",
                        type: "text",
                        width: 250,
                    },
                    { key: "samplename", title: "样品名称", type: "text", width: 150 },
                    { key: "unit", title: "单位", type: "text", width: 150 },
                    { key: "price", title: "价格", type: "text", width: 80 },
                    {
                        key: "_add_time",
                        title: "录入时间",
                        type: "time",
                        width: 160,
                        valueFormat: "MM月dd日 ",
                        width: 80,
                    },
                ],
                // 多选框选中的值
                multipleSelection: [],
                // 当前高亮的记录
                selectItem: "",
            },
            // 表格相关结束 -----------------------------------------------------------
            // 表单相关开始 -----------------------------------------------------------
            // 查询表单请求数据
            queryForm1: {
                // 查询表单数据源，可在此设置默认值
                formData: {
                    _add_time: []
                },
                // 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
                columns: [

                    {
                        key: "_add_time",
                        title: "填单时间",
                        type: "datetimerange",
                        width: 380,
                        mode: "[]",
                    },
                    {
                        key: "client", title: "委托单位/人", type: "select", placeholder: "请选择委托单位",
                        filterable: true,
                        data: [],
                        props: { list: "rows", value: "_id", label: "name" },
                        width: 150,
                        mode: "in",
                    },


                    { key: "detection_user", title: "检测人", type: "text", width: 160, mode: "in", },
                ],
            },
            queryForm2: {
                // 查询表单数据源，可在此设置默认值
                formData: {},
                // 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
                columns: [
                    {
                        key: "detection_category",
                        title: "项目名称",
                        type: "datetimerange",
                        width: 380,
                        mode: "=",
                    },
                    {
                        key: "_add_time",
                        title: "填单时间",
                        type: "datetimerange",
                        width: 380,
                        mode: "[]",
                    },
                ],
            },
            form1: {
                // 表单请求数据，此处可以设置默认值
                data: {},
                // 表单属性
                props: {
                    // 表单请求地址
                    action: "",
                    // 表单字段显示规则
                    columns: [
                        {
                            key: "samplename",
                            title: "商品名称",
                            type: "text",
                            placeholder: "请输入商品规格名称",
                            width: 300,
                        },
                        {
                            key: "count",
                            title: "数量",
                            type: "number",
                            placeholder: "请输入排序权重",
                            width: 200,
                        },

                    ],
                    // 表单验证规则
                    rules: {
                        name: [
                            // 必填
                            {
                                required: true,
                                message: "商品规格名称不能为空",
                                trigger: ["blur", "change"],
                            },
                        ],
                        // sort: [
                        // 	// 必填
                        // 	{ required: true, message: "排序不能为空", trigger: ['blur', 'change'] }
                        // ],
                    },
                    // add 代表添加 update 代表修改
                    formType: "",
                    // 是否显示表单的弹窗
                    show: false,
                },
            },
            // 其他弹窗表单
            formDatas: {},
            // 表单相关结束 -----------------------------------------------------------
        };
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        let { weekStart, weekEnd } = vk.pubfn.getCommonTime(new Date());
        this.queryForm1.formData._add_time = [weekStart, weekEnd];
        vk.callFunction({
            url: `admin/base/client/sys/getalllist`,
            need_user_info: false,
            data: {
                page: 1,
                pageSize: 9999
            },
            success: (data) => {
                this.clients = data.rows;
                // if (this.$hasRole('admin') || this.$hasRole('ROLE_ZZ'))
                // 	this.clients = data.rows;
                // else {
                // 	this.clients = data.rows.filter(x => includes(delivery_company, x._id))

                // }
            }
        });
        that.init(options);

    },
    onUnload() {
        // 返回false阻止页面被销毁
        return false;
    },

    // 函数
    methods: {
        // 页面数据初始化函数git
        init(options) {
            originalForms["form1"] = vk.pubfn.copyObject(that.form1);
        },
        // 页面跳转
        pageTo(path) {
            vk.navigateTo(path);
        },
        // 表单重置
        resetForm() {
            vk.pubfn.resetForm(originalForms, that);
        },
        // 搜索
        search() {
            that.$refs.table1.search();
        },
        // 刷新
        refresh() {
            that.$refs.table1.refresh();
        },
        // 获取当前选中的行的数据
        getCurrentRow() {
            return that.$refs.table1.getCurrentRow();
        },
        // 监听 - 行的选中高亮事件
        currentChange(val) {
            that.table1.selectItem = val;
        },
        // 当选择项发生变化时会触发该事件
        selectionChange(list) {
            that.table1.multipleSelection = list;
        },


        exportexl() {
            this.$refs.xTable.exportData()
        },
        footerDetailMethod({ columns, data }) {

            return this.base_footerDetailMethod(columns, data, ['price']);
        },
        base_footerDetailMethod(columns, data, keys) {
            return [
                columns.map((column, columnIndex) => {
                    if (columnIndex === 0) {
                        return '合计：';
                    }
                    for (let i = 0; i < keys.length; i++) {
                        let key = keys[i];
                        if ([key].includes(column.property)) {
                            let res = 0;
                            for (let i = 0; i < data.length; i++) {
                                if (data[i][key] != undefined) {
                                    res = add(res, data[i][key]);

                                }
                            }
                            return res;
                        }
                    }
                    return null;
                })
            ];
        },
    },
    // 监听属性
    watch: {
        clients(newval, oldval) {

      
            let _obj1 = that.queryForm1.columns.find(x => x.key == 'client')

            console.log(_obj1)
            that.$set(_obj1, 'data', newval)
        },
    },
    // 过滤器
    filters: {},
    // 计算属性
    computed: {},
};
</script>

<style lang="scss" scoped>
.page-body {}
</style>
