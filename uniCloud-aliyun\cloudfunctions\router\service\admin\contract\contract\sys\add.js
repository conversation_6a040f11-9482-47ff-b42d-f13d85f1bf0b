module.exports = {
	/**
	 * 添加单条数据
	 * @url admin/kong/sys/add 前端调用的url参数地址
	 * data 请求参数 说明
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: 'ok' };
		// 业务逻辑开始-----------------------------------------------------------
		let {
			
			name,
			client,
			client_name,
			contact_name,
			mobile,
			address,
			check_range,
			isHasImg,
			report_send_type,
			pay_type,
			sampleHandling,
			IsreportDetermined,
			check_type,
			isSub,
			detectionFrom,
			isAgreementCustomers,
			contractNumber,
			inspections,
			invoicing,
			discount,
			totalSettlement,
			realtotal,
			sort,
			sample_list,

			originfile,
			invoicefile,
		} = data;
		// 这里需要把 params1 params2 params3 改成你数据库里允许用户添加的字段
		let dbName = "tm-contract";
		let info = await vk.baseDao.findByWhereJson({
			dbName: dbName,
			whereJson: {
				name: name,
			}
		});
		res.id = await vk.baseDao.add({
			dbName,
			dataJson: {
			
				name,
				client,
				client_name,
				contact_name,
				mobile,
				address,
				check_range,
				isHasImg,
				report_send_type,
				pay_type,
				sampleHandling,
				IsreportDetermined,
				check_type,
				isSub,
				detectionFrom,
				isAgreementCustomers,
				contractNumber,
				inspections,
				invoicing,
				discount,
				totalSettlement,
				realtotal,
				sort,
				originfile,
				invoicefile,
			}
		});

		if (sample_list.length > 0) {

			let _add_sample_list = sample_list.map(x => {
				return {
					'contract_id': res.id,
					'client_name': client_name,
					...x
				}
			})
			if (vk.pubfn.isNotNull(_add_sample_list)) {
				await vk.baseDao.adds({
					dbName: "tm-contract-sample-testing",// 表名
					dataJson: _add_sample_list
				});
			}

		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}
