module.exports = {
  /**
   * 查询多条记录 分页
   * @url admin/kong/sys/getList 前端调用的url参数地址
   * data 请求参数 说明
   * @param {Number}         pageIndex 当前页码
   * @param {Number}         pageSize  每页显示数量
   * @param {Array<Object>}  sortRule  排序规则
   * @param {object}         formData  查询条件数据源
   * @param {Array<Object>}  columns   查询条件规则
   * res 返回参数说明
   * @param {Number}         code      错误码，0表示成功
   * @param {String}         msg       详细信息
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: '' };
    // 业务逻辑开始-----------------------------------------------------------
    let dbName = "tm-client";
    let _whereJson = undefined;
    if (!userInfo.role.includes('admin')) {
      if (userInfo.role.includes('ROLE_PSGS')) {
        _whereJson = {

          name: userInfo.nickname

        }
      } else if (userInfo.role.includes('ROLE_PT') || userInfo.role.includes('ROLE_ZZ')) {
          // userInfo.role.includes('ROLE_ZZ') ||
          _whereJson = {
              _id: _.in(userInfo.delivery_company)
          };
      }
    }
    res = await vk.baseDao.getTableData({
      dbName,
      data,
      whereJson: _whereJson,
      sortArr: [{ "name": "name", "type": "desc" }],
      foreignDB: [
        // {
        //   dbName: "tm-organization",
        //   localKey: "organization_id",
        //   foreignKey: "_id",
        //   as: "organization_info",
        //   limit: 1,
        // }, 
        {
          dbName: "tm-client-price",
          localKey: "_id",
          foreignKey: "client_id",
          as: "price_list",
          sortArr: [{ "name": "category_name", "type": "desc" }],
          limit: 100,
        },
        {
          dbName: "uni-id-users",
          localKey: "_id",
          foreignKey: "delivery_company",
          foreignKeyType: "array",
          fieldJson: {
            _id: true,
            nickname: true,
          },
          as: "userInfo",
          limit: 50
        }

      ],

    });
    return res;
  }

}
