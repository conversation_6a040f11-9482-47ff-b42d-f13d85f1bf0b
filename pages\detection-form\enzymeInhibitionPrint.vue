<template>
    <vk-data-dialog v-model="value.show" :title="page.title" :top="page.top" :width="page.width" mode="form" max-height="800px" width="860px" @open="onOpen" @closed="onClose" :close-on-click-modal="true">
        <div id="tableId-0" ref="printContent">
            <div v-for="(v, i) in value.printData" :key="i" style="page-break-after: always">
                <div style="width: 850px; align-content: center">
                    <div style="z-index: -1; display: flex; -ms-flex-align: center; align-items: center; -ms-flex-pack: center;  -ms-flex-direction: column; flex-direction: column" :style="{ height: getPageHeight(v) + 'px' }">
                        <div>
                            <div style="display: flex; width: 100%; font-family: 宋体; margin-top: 20px">
                                <div style="width: 185px">
                                    <img src="@/static/print-log.png" height="80px" width="170px" />
                                </div>
                                <div style="display: flex; flex-direction: column; justify-content: center">
                                    <div>
                                        <span style="font-size: 30px; color: black; display: ruby-text; font-weight: bold; line-height: 34px; flex-wrap: nowrap; width: 100%">深圳市金阅检测科技有限责任公司</span>
                                    </div>
                                    <div style="color: black; font-size: 23px; font-weight: bold; display: ruby-text">Shenzhen King Eye Testing Technology Co., Ltd</div>
                                </div>
                            </div>
                        </div>
                        <div style="width: 95%; padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px"></div>
                        <div style="width: 95%; padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px"></div>
                        <div style="position: relative; width: 95%; font-family: 宋体">
                            <div style="font-size: 30px !important; font-weight: bold; font-weight: bold; color: black; margin-top: 10px; text-align: center">检 测 报 告</div>
                        </div>
                        <div style="width: 95%">
                            <div style="margin-bottom: 5px; display: flex; justify-content: space-between">
                                <div style="display: flex; justify-content: start">
                                    <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">报告编号：</span>
                                    <span style="font-size: 16px; font-family: 宋体; color: black">{{ v.no }}</span>
                                </div>

                                <div style="width: 120px">
                                    <span style="display: ruby-text">
                                        {{ `第${1}页 / 共${getPageCount(v)}页` }}
                                    </span>
                                </div>
                            </div>
                            <div>
                                <table class="daying" style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr>
                                        <td colspan="4" align="center" style="height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">客户信息及检测信息</span></td>
                                        <td rowspan="6" class="qrcode-cell" style="border: 1px solid #000; width: 20%">
                                            <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%">
                                                <vue-qr :logoSrc="logourl" :margin="2" :text="`https://static-host-uebpmzce-web.gzg.sealos.run/#/pages/qr_info/em?no=${v.no}`" :size="120"></vue-qr>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">委托单位名称</span></td>
                                        <td align="center" colspan="3" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.client_name }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">委托单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.address || '广东省佛山市三水区耀华路8号' }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受 检 单 位</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.submitUser }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受检单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ (v.submituser_address || v.addressps) ? (v.submituser_address || v.addressps) : (v.client_name === v.submitUser ? v.address : '') }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">接 收 日 期</span></td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat(v.detect_time, 'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样 品 状 态</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">鲜样正常</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">验 证 日 期</span></td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat(v.detect_time, 'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检 测 类 型</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">委托检测</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 12px">扫描二维码辨别报告真伪</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div style="align-items: center; font-size: 18px; font-weight: bold; font-weight: bold; margin-top: 6px; margin-bottom: 6px; font-family: 宋体; color: black">检 测 结 果</div>
                        <div style="width: 95%">
                            <div>
                                <table class="daying" style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr style="height: 30px">
                                        <td align="center" width="4%" style="border: 1px solid #000; height: 15px"><span style="font-weight: bold; font-size: 16px; font-family: 黑体">序号</span></td>
                                        <td align="center" width="10%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品编号</span></td>
                                        <td align="center" width="15%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品名称</span></td>
                                        <td align="center" width="13%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测项目</span></td>
                                        <td align="center" width="10%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">标准限值 (%)</span></td>
                                        <td align="center" width="10%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">抑制率 (%)</span></td>
                                        <td align="center" width="8%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">判定结论</span></td>
                                        <td align="center" width="8%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">备注</span></td>
                                    </tr>

                                    <!-- 动态行高和字体大小 -->
                                    <tr v-for="(detail, index) in getsubList(v.sample_list, 1)" :key="index" :style="{ height: calcRowHeight(v.sample_list.length) + 'px', fontSize: calcFontSize(v.sample_list.length) + 'px' }">
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ index + 1 }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.sampleno }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.samplename }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.detection_category }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.standard_limit }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.inhibition_rate }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px; ">
                                            {{ detail.conclusion || getConclusion(detail) }}
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 13px" :style="{ 'font-size': '13px' }">
                                            {{ detail.remark }}
                                        </td>
                                    </tr>

                                    <!-- 检测方法和判定标准 -->
                                    <tr>
                                        <td colspan="2" align="center" style="border: 1px solid #000; height: 20px">
                                            <span style="color: black; font-weight: bold; font-size: 15px; font-family: 黑体">检测方法</span>
                                        </td>
                                        <td colspan="2" align="center" style="border: 1px solid #000; height: 20px">
                                            <span style="font-size: 15px">酶抑制法</span>
                                        </td>
                                        <td colspan="1" align="center" style="border: 1px solid #000; height: 20px">
                                            <span style="color: black; font-weight: bold; font-size: 15px; font-family: 黑体">检测依据</span>
                                        </td>
                                        <td colspan="3" align="center" style="border: 1px solid #000; height: 20px">
                                            <span style="font-size: 15px">参考GB/T 5009.199-2003</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" align="center" style="border: 1px solid #000; height: 20px">
                                            <span style="color: black; font-weight: bold; font-size: 15px; font-family: 黑体">判定标准</span>
                                        </td>
                                        <td colspan="6" align="center" style="border: 1px solid #000; height: 20px">
                                            <span style="font-size: 15px">抑制率（%）< 50为阴性；抑制率（%）≥ 50为阳性</span>
                                        </td>
                                    </tr>
                                </table>

                                <div style="border: 1px solid #000; border-top: none; padding: 5px 10px; font-size: 13px; font-family: 黑体; color: #000; text-align: left">
                                    <div>注：请客户仔细阅读检测报告的申明</div>
                                    <div>1、报告无快检专用章无效；报告无检测人、审核人签名无效，报告经涂改、增删无效；</div>
                                    <div>2、以上样品信息均由客户提供，本司不承担其信息准确性的责任；</div>
                                    <div>3、本报告只对送检样品检测结果负责。以上检测结果为快速检测定性检测结果，不具备法律效力，仅供客户参考。</div>
                                    <div>4、委托单位对本检测报告有异议，请在收到报告之日或指定领取报告之日起，三个工作日内提出申诉，逾期不子受理。</div>
                                </div>

                                <div v-if="v.sample_list.length > pageSzie" style="width: 100%; font-size: 16px; font-weight: bold; margin-top: 3%; margin-bottom: 10%; position: relative">
                                    <div style="z-index: 999; position: absolute; transform: translateX(-140%) translateY(-200px); margin-left: 100%; background-color: transparent">
                                        <img src="@/static/gz.png" width="150px;" height="150px;" />
                                    </div>
                                </div>
                                <print_footer v-if="v.sample_list.length > pageSzie" :pageIndex="`第一页`"></print_footer>
                                <print_content v-if="v.sample_list.length > pageSzie" :pageSzie="pageSzie" :data="getsubList(v.sample_list, 2)" :isLast="v.sample_list.length > pageSzie && v.sample_list.length < pageSzie2" :pageIndex="`第二页`" />
                                <print_content v-if="v.sample_list.length > pageSzie2" :pageSzie="pageSzie2" :data="getsubList(v.sample_list, 3)" :isLast="v.sample_list.length > pageSzie2 && v.sample_list.length < pageSzie3" :pageIndex="`第三页`" />
                                <print_content v-if="v.sample_list.length > pageSzie3" :pageSzie="pageSzie3" :data="getsubList(v.sample_list, 4)" :isLast="v.sample_list.length > pageSzie3 && v.sample_list.length < pageSzie4" :pageIndex="`第四页`" />
                                <print_content v-if="v.sample_list.length > pageSzie4" :pageSzie="pageSzie4" :data="getsubList(v.sample_list, 5)" :isLast="v.sample_list.length > pageSzie4 && v.sample_list.length < pageSzie5" :pageIndex="`第五页`" />
                            </div>
                        </div>

                        <!--?印章-->
                        <div style="width: 95%; font-size: 16px; font-weight: bold; margin-bottom: 10%; position: relative" :style="{ 'margin-top': v.sample_list.length > pageSzie ? '-2px' : '2px' }">
                            <div style="z-index: 999; position: absolute; transform: translateX(-140%) translateY(-50%); margin-left: 100%; background-color: transparent">
                                <img src="@/static/gz.png" width="150px;" height="150px;" />
                            </div>
                            <div style="margin-left: 100%; z-index: 2; white-space: nowrap; position: absolute; transform: translateX(-100%); font-family: 黑体">此处未盖本公司快检专用章，则本报告无效。</div>
                        </div>
                        <!--?印章-->
                        <div style="display: flex; flex-direction: row; justify-content: space-between; width: 95%; z-index: 1">
                            <div style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                                <div>检测人:</div>
                                <div style="position: absolute; left: 28%; top: -80%">
                                    <span>
                                        <img v-if="$fn.isNotNull(v.detection_user_info.sign_image)" width="100px" height="40px" :src="v.detection_user_info.sign_image" />
                                    </span>
                                </div>
                            </div>
                            <div style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                                <div>审核人:</div>
                                <div style="position: absolute; left: 28%; top: -60%">
                                    <img src="@/static/shr.png" width="100px" height="40px" />
                                </div>
                            </div>
                            <div style="width: 34%; font-size: 16px; font-weight: bold; font-family: 黑体">
                                <span>签发日期:</span>
                                <span>{{ vk.pubfn.timeFormat(v.detect_time, 'yyyy年MM月dd日') }}</span>
                            </div>
                        </div>

                        <div style="margin-top: auto; width: 95%; padding-bottom: 20px">
                            <div style="display: flex; justify-content: center; text-align: center; font-size: 10px" v-if="v.sample_list.length > pageSzie && v.sample_list.length < pageSzie2">第二页</div>
                            <div style="margin-top: 13px; margin-bottom: 10px; text-align: center; font-size: 18px">***报告完结***</div>
                            <div style="padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px"></div>
                            <div style="padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px"></div>
                            <div style="display: flex; align-content: space-between; color: #606266; margin-top: 0px">
                                <div style="font-size: 12px; font-family: 黑体; display: ruby-text">Hotline 0755-28380866 深圳市金阅检测科技有限责任公司</div>
                                <div style="margin-left: 16%; font-size: 12px; text-align: end; font-family: 黑体; align-content: flex-end; color: #606266">地址：深圳市大鹏新区大鹏街道布新社区布新村工业大道2号C401</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template v-slot:footer="{ close }">
            <!--这里是底部插槽-->
            <el-button @click="advancedPrint">打 印</el-button>
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="close">确 定</el-button>
        </template>
    </vk-data-dialog>
</template>

<script>
import VueQr from 'vue-qr';
import Print from 'print-js';
import print_content from './print_content_em.vue';
import print_footer from './print_footer.vue';
import printbase from '@/mixins/printbase.js';
import { slice } from 'lodash';
var that; // 当前页面对象
var vk = uni.vk; // vk实例
export default {
    components: {
        VueQr,
        print_footer,
        print_content
    },
       mixins: [ printbase],
    props: {
        value: {
            Type: Object,
            default: function () {
                return {
                    show: false,
                    printData: []
                };
            }
        }
    },
    data: function () {
        // 组件创建时,进行数据初始化
        return {
            page: {
                title: '酶抑制报告打印',
                submitText: '确定',
                cancelText: '关闭',
                showCancel: true,
                top: '3vh',
                width: '806px'
            },
                        pageSzie: 18,
            pageSzie2: 49,
            pageSzie3: 80,
            pageSzie4: 111,
            pageSzie5: 142,

        };
    },
    mounted() {
        that = this;
        that.init();
    },
    methods: {
        
        // 获取抑制率（如果没有设置，则从其他字段提取或返回默认值）
        getInhibitionRate(detail) {
            // 如果有detection_include字段且是数字，可能存储了抑制率
            if (vk.pubfn.isNotNull(detail.detection_include) && !isNaN(detail.detection_include)) {
                return detail.detection_include;
            }
        },
        // 获取判定结论
        getConclusion(detail) {
            if (detail.result === '阴性' || detail.yxresult === '阴性') {
                return '阴性';
            }
        },

       
    },
    // 监听属性
    watch: {
        'value.printData'(val) {
            val.forEach(element => {
                if (element.sample_list.length < 10) {
                    const row = 10 - element.sample_list.length;
                    for (let i = 0; i < row; i++) {
                        element.sample_list.push({
                            sampleno: i == 0 ? '以下空白' : ''
                        });
                    }
                } else if (element.sample_list.length > 10 && element.sample_list.length < 18) {
                    const row = 18 - element.sample_list.length;
                    for (let i = 0; i < row; i++) {
                        element.sample_list.push({
                            sampleno: i == 0 ? '以下空白' : ''
                        });
                    }
                } else if (element.sample_list.length > 10 && element.sample_list.length < 30) {
                    const row = 30 - element.sample_list.length;
                    for (let i = 0; i < row; i++) {
                        element.sample_list.push({
                            sampleno: i == 0 ? '以下空白' : ''
                        });
                    }
                } else if (element.sample_list.length > 10 && element.sample_list.length < 40) {
                    const row = 40 - element.sample_list.length;
                    for (let i = 0; i < row; i++) {
                        element.sample_list.push({
                            sampleno: i == 0 ? '以下空白' : ''
                        });
                    }
                }
            });
        }
    },
    // 计算属性
    computed: {
        first15SampleList() {
            return this.sample_list.slice(0, 15);
        },
        // 使用计算属性来获取剩余的样本
        remainingSampleList() {
            return this.sample_list.slice(15);
        }
    }
};
</script>

<style lang="scss" scoped>
.page-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
}

@media print {
    .last-page {
        page-break-after: avoid !important;
    }
}

.page-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
}

.watermark-item {
    position: absolute;
    transform: rotate(-30deg);
    opacity: 0.1;
    font-size: 40px;
    font-weight: bold;
    color: #000;
    white-space: nowrap;
}
</style>

