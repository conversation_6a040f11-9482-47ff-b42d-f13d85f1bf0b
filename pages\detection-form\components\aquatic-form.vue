<template>
    <vk-data-dialog v-model="Form.props.show" :title="Form.props.title" width="1250px" mode="form"
        :close-on-click-modal="false">
        <vk-data-form v-model="Form.data" size="mini" ref="form1" :rules="Form.props.rules" :action="Form.props.action"
            :before-action="Form.props.beforeAction" :form-type="Form.props.formType" :columns="Form.props.columns"
            label-width="120px" @success="
                Form.props.show = false;
            refresh();
            " :inline="true" :columnsNumber="2">
            <template v-slot:sample_list="{ form, keyName }">
                <el-button type="success" size="mini" :disabled="!$fn.isNotNull(Form.data.sampleid)"
                    icon="el-icon-circle-plus-outline" style="margin: 10px 5px"
                    @click="insertEvent(-1)">添加检测项目</el-button>
                <vxe-table ref="xTable" keep-source border resizable show-overflow size="small" :data="form[keyName]"
                    :edit-config="{ trigger: 'click', mode: 'cell', activeMethod: activeCellMethod }">
                    <vxe-table-column fixed="left" type="seq" width="80"></vxe-table-column>
                    <vxe-table-column field="detection_category" width="200" title="检测项目" align="left"
                        :edit-render="{ name: 'visible', autoselect: true }">
                        <template v-slot:edit="scope">
                            <vk-data-input-select size="small" v-model="scope.row.detection_category"
                                :localdata="Form.data.detection_category_list" placeholder="请选择"
                                :props="{ value: '_id', label: 'name' }" width="180px" @change="(val, formData, column, index, option) => {
                                    handelDetection_categoryChane(val, formData, scope.row);
                                }
                                    "></vk-data-input-select>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column field="detection_standard" title="检测方法" align="left" width="180"
                        :edit-render="{ name: 'visible', autoselect: true }">
                        <template v-slot:edit="scope">
                            <vxe-input v-model="scope.row.detection_standard" type="text"></vxe-input>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column field="detection_include" min-width="200" title="标准要求" align="left" width="150"
                        :edit-render="{ name: 'visible' }">
                        <template v-slot:edit="scope">
                            <vxe-input v-model="scope.row.standardRequir" type="text"></vxe-input>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column field="detection_include" min-width="200" title="检测限" align="left" width="150"
                        :edit-render="{ name: 'visible' }">
                        <template v-slot:edit="scope">
                            <vxe-input v-model="scope.row.detection_include" type="text"></vxe-input>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column field="result" title="测试结果" align="left" width="120"
                        :edit-render="{ name: 'visible', autoselect: true }">
                        <template v-slot:edit="scope">
                            <vk-data-input-select size="small" v-model="scope.row.result_value" :localdata="[
                                { value: 1, label: '合格' },
                                { value: 0, label: '不合格' }
                            ]" placeholder="请选择" width="110px" @change="(val, formData, column, index, option) => {
                                if (scope.row.result_value == 1) {
                                    scope.row.result = `阴性`;
                                    scope.row.yxresult = '阴性';
                                } else {
                                    scope.row.result = `经检测${Form.data.detection_standard_name}已超出相关要求`;
                                    scope.row.yxresult = `经检测${Form.data.detection_standard_name}已超出相关要求`;
                                }
                            }
                                "></vk-data-input-select>
                        </template>
                    </vxe-table-column>

                    <vxe-column title="操作" width="150">
                        <template #default="{ row }">
                            <template>
                                <!-- <vxe-button status="success" v-if="row.isSave == undefined || row.isSave != 1" type="error" @click="marksur(row)">确定</vxe-button>
                                <vxe-button status="danger" @click="removeEvent(row)">删除</vxe-button> -->
                            </template>
                        </template>
                    </vxe-column>
                </vxe-table>
            </template>
        </vk-data-form>
    </vk-data-dialog>
</template>

<script>
import { remove, concat, find, includes, unionBy, orderBy } from 'lodash';
var that; // 当前页面对象
var vk = uni.vk; // vk实例
// 表单初始化数据

export default {
    props: {
        Form: {
            Type: Object,
            default: function () {
                return {
                    props: {
                        title: ''
                    },
                    data: {}
                };
            }
        },
        Goods: {
            Type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        // 页面数据变量
        return {};
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
    },
    onUnload() {
        // 返回false阻止页面被销毁
        return false;
    },
    // 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
    onReady() { },
    // 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
    onShow() { },
    // 监听 - 页面每次【隐藏时】执行(如：返回)
    onHide() { },
    // 函数
    methods: {
        activeCellMethod({ row, rowIndex }) {

            if (row.isSave != undefined && row.isSave == 1)
                return false;
            else
                return true;
        },


    },
    // 监听属性
    watch: {
        // 'Form.data.'

    },
    // 过滤器
    filters: {},
    // 计算属性
    computed: {}
};
</script>

<style lang="scss" scoped>
.page-body {}
</style>
