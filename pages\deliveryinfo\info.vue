<template>
    <view class="page">
        <!-- 页面内容开始 -->
        <vk-data-page-header title="配送公司简介"></vk-data-page-header>
        <view class="page-body" style="max-width: 950px;">
            <vk-data-form v-model="form1.data" :rules="form1.props.rules" :action="form1.props.action"
                :before-action="form1.props.beforeAction" :form-type="form1.props.formType"
                :columns='form1.props.columns' :loading.sync="form1.props.loading" label-width="200px"
                @cancel="onCancel" @success="onFormSuccess" :submit-disabled="!$hasRole('ROLE_PSGS') "></vk-data-form>


        </view>
        <!-- 页面内容结束 -->
    </view>
</template>

<script>
var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
export default {
    data() {
        // 页面数据变量
        return {
            // 表单相关开始-----------------------------------------------------------
            form1: {
                // 表单请求数据，此处可以设置默认值
                data: {
                    enterprise_name: '',
                },
                // 表单属性
                props: {
                    // 表单请求地址
                    action: "admin/base/deliveryinfo/sys/update",
                    beforeAction: (formData) => { 
                        formData.enterprise_name = vk.getVuex('$user.userInfo').nickname;
                        return formData;
                    },
                    // 表单字段显示规则
                    columns: [

                        {
                            key: "enterprise_info", title: "企业介绍", type: "textarea",
                            autosize: { minRows: 4, maxRows: 10 },
                            maxlength: 200,
                            showWordLimit: true
                        },
                        {
                            key: "enterprise_image", title: "企业图片", type: "image", limit: 3,
                            tips: '最大支持上传三张图片'
                        },
                        {
                            key: "job_description", title: "作业说明", type: "textarea",
                            autosize: { minRows: 4, maxRows: 10 },
                            maxlength: 200,
                            showWordLimit: true
                        },
                        { key: "job_image", title: "作业图片", type: "image", limit: 3, tips: '最大支持上传三张图片' },
                        { key: "antivirus_records", title: "仓库杀毒,杀菌记录表", type: "image", limit: 1 },
                        { key: "statement_qualification", title: "资质说明", type: "image", limit: 10, tips: '建议上传三张图片' },
                        { key: "driving_license", title: "车辆行驶证", type: "image", limit: 1 },
                        { key: "person1", title: "配送人员1", type: "image", limit: 1 },
                        { key: "person2", title: "配送人员2", type: "image", limit: 1 },
                    ],
                    // 表单验证规则
                    rules: {

                    },
                    // add 代表添加 update 代表修改
                    formType: '',
                    // 是否显示表单1 的弹窗
                    show: false,
                    // 表单是否在请求中
                    loading: false
                }
            },
            // 表单相关结束-----------------------------------------------------------

        }
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        that.init(options);
        // console.log(vk.getVuex('$user.userInfo').nickname)
        // this.form1.data.enterprise_name = vk.getVuex('$user.userInfo').nickname
    },
    // 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
    onReady() {

    },
    // 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
    onShow() {


    },
    // 监听 - 页面每次【隐藏时】执行(如：返回)
    onHide() {


    },
    // 函数
    methods: {
        // 页面数据初始化函数
        init(options) {
            if (this.$hasRole('ROLE_PSGS')) {
                vk.callFunction({
                    url: 'admin/base/deliveryinfo/sys/getinfo',
                    title: '请求中...',
                    data: {
                        columns: [{
                            key: "user_id",
                            mode: "=",
                            title: "user_id",
                            type: "text",
                        }
                        ],
                        formData: {
                            user_id: vk.getVuex('$user.userInfo')._id,
                        },

                    },
                    success: (data) => {
                        if (this.$fn.isNull(data.data))
                            this.form1.data = {}
                        else
                            this.form1.data = data.data;

                    }
                });  
            }

        },
        onCancel() {
       
            vk.menuTabs.closeCurrent();
        },
        onFormSuccess() {
            this.init();
        }
    },
    // 过滤器
    filters: {

    },
    // 计算属性
    computed: {

    }
}
</script>
<style lang="scss" scoped></style>
