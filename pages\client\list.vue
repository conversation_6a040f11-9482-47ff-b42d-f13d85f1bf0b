<template>
	<view class="page-body">
		<!-- 页面内容开始 -->

		<!-- 表格搜索组件开始 -->
		<vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" size="mini"
			@search="search"></vk-data-table-query>
		<!-- 表格搜索组件结束 -->

		<!-- 自定义按钮区域开始 -->
		<view>
			<el-row>
				<el-button type="success" size="mini" icon="el-icon-circle-plus-outline" @click="addBtn">添加</el-button>
				<el-button type="success" size="small" icon="el-icon-circle-plus-outline"
					@click="addByJsonBtn">通过JSON数组批量导入</el-button>
				<!-- 批量操作
				<el-dropdown v-if="table1.multipleSelection" :split-button="false" trigger="click" @command="batchBtn">
					<el-button type="danger" size="mini" style="margin-left: 20rpx;"
						:disabled="table1.multipleSelection.length === 0">
						批量操作<i class="el-icon-arrow-down el-icon--right"></i>
					</el-button>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item :command="1">批量操作1</el-dropdown-item>
						<el-dropdown-item :command="2">批量操作2</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown> -->
			</el-row>
		</view>
		<!-- 自定义按钮区域结束 -->

		<!-- 表格组件开始 -->
		<vk-data-table ref="table1" :rowNo="true" :border="true" :action="table1.action" :columns="table1.columns"
			:query-form-param="queryForm1" size="mini" :right-btns="['detail_auto', 'update', 'delete']"
			:selection="true" :pagination="true" @update="updateBtn" @delete="deleteBtn" @current-change="currentChange"
			@selection-change="selectionChange">

		</vk-data-table>
		<!-- 表格组件结束 -->

		<!-- 添加或编辑的弹窗开始 -->
		<vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="800px" mode="form"
			:close-on-click-modal="false">
			<vk-data-form v-model="form1.data" ref="form1" :rules="form1.props.rules" :action="form1.props.action"
				:form-type="form1.props.formType" :columns='form1.props.columns' label-width="120px"
				@success="form1.props.show = false; refresh();">
				<template v-slot:price_list="{ form, keyName }">
					<vxe-table ref="xTable" keep-source border resizable show-overflow size="small"
						:data="form[keyName]" :edit-config="{ trigger: 'click', mode: 'cell' }">
						<vxe-table-column fixed="left" type="seq" width="80"></vxe-table-column>

						<vxe-table-column field="category_name" min-width="350" title="检测类型" align="left" width="350">

						</vxe-table-column>
						<vxe-table-column field="price" min-width="150" title="单价(元)" align="left" width="150"
							:edit-render="{ name: 'visible', }">
							<template v-slot:edit="scope">
								<vxe-input v-model="scope.row.price" type="number"></vxe-input>
							</template></vxe-table-column>
					</vxe-table>
				</template>
			</vk-data-form>
		</vk-data-dialog>
		<!-- 添加或编辑的弹窗结束 -->
		<vk-data-dialog v-model="form2.props.show" :title="form2.props.title" width="700px" mode="form"
			:close-on-click-modal="false">
			<vk-data-form v-model="form2.data" :rules="form2.props.rules" :action="form2.props.action"
				:form-type="form2.props.formType" :columns='form2.props.columns' label-width="120px"
				:before-action="form2.props.beforeAction"
				@success="form2.props.show = false; refresh();"></vk-data-form>
		</vk-data-dialog>
		<!-- 页面内容结束 -->
	</view>
</template>

<script>
import { remove, concat, find, includes, unionBy } from 'lodash'
var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
var originalForms = {};						// 表单初始化数据

export default {
	data() {
		// 页面数据变量
		return {
			// 页面是否请求中或加载中
			loading: false,
			organizations: [],
			// init请求返回的数据
			data: {

			},
			// 表格相关开始 -----------------------------------------------------------
			table1: {
				// 表格数据请求地址
				action: "admin/base/client/sys/getList",
				// 表格字段显示规则
				columns: [
					// { key:"_id", title:"id", type:"text", width:220 },

					{ key: "name", title: "配送公司名称", type: "text", width: 400, sortable: "custom" },
					{ key: "organization_id", title: "组织名称", type: "text", width: 180, formatter: this.organizationfomattr },
					{ key: "userInfo", title: "已分配人员", type: "text", width: 250, formatter: this.userInfoFomattr },
					{
						key: "is_productiondate", title: "生产日期", width: 80, type: "switch", width: 120, activeValue: true, inactiveValue: false,

						watch: (res) => {
							let { value, row, change } = res;
							vk.callFunction({
								url: "admin/base/client/sys/update",
								title: value ? "启用中..." : "关闭中...",
								data: {
									_id: row._id,
									is_productiondate: value
								},
								success: data => {
									change(value);
								}
							});
						}
					},
					{ key: "thumb", title: "附件", type: "image", show: ["detail"] },
					{ key: "sort", title: "排序", type: "number", width: 100, sortable: "custom" },

					// { key:"_add_time", title:"添加时间", type:"time", width:160, sortable:"custom"  },
					// { key:"_add_time", title:"距离现在", type:"dateDiff", width:120 },
				],
				// 多选框选中的值
				multipleSelection: [],
				// 当前高亮的记录
				selectItem: ""
			},
			// 表格相关结束 -----------------------------------------------------------
			// 表单相关开始 -----------------------------------------------------------
			// 查询表单请求数据
			queryForm1: {
				// 查询表单数据源，可在此设置默认值
				formData: {

				},
				// 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
				columns: [
					{ key: "name", title: "配送公司名称", type: "text", width: 160, mode: "%%" },

				]
			},
			form1: {
				// 表单请求数据，此处可以设置默认值
				data: {

				},
				// 表单属性
				props: {
					// 表单请求地址
					action: "",
					// 表单字段显示规则
					columns: [

						{ key: "name", title: "配送公司名称", type: "text", placeholder: "请输入类别名称", width: 300 },
						{
							key: "organization_id", title: "组织名称", type: "tree-select", width: 500,
							action: "admin/base/organization/sys/getList",
							props: { list: "rows", value: "_id", label: "name", children: "children" }, needSave: true,
						},
						{ key: "address", title: "地址", type: "text", width: 600, sortable: "custom" },
				    	{ key: "addressps", title: "配送地址", type: "text", width: 600, sortable: "custom" },
						{ key: "thumb", title: "附件", type: "image", limit: 3 },
						{
							key: "is_productiondate", title: "生产日期", width: 80, type: "switch", width: 120, activeValue: true, inactiveValue: false,
						},

						{ key: "sort", title: "排序", type: "number", placeholder: "请输入排序权重", width: 300 },
						{ key: "price_list", title: "检测单价", type: "text" },
					],
					// 表单验证规则
					rules: {
						name: [
							// 必填
							{ required: true, message: "配送公司名称不能为空", trigger: ['blur', 'change'] }
						],
						organization_id: [
							// 必填
							{ required: true, message: "组织名称不能为空", trigger: ['blur', 'change'] }
						],
						// sort: [
						// 	// 必填
						// 	{ required: true, message: "排序不能为空", trigger: ['blur', 'change'] }
						// ],
					},
					// add 代表添加 update 代表修改
					formType: "",
					// 是否显示表单的弹窗
					show: false
				}
			},
			form2: {
				// 表单请求数据，此处可以设置默认值
				data: {

				},
				// 表单属性
				props: {
					// 表单请求地址
					action: "",
					// 表单字段显示规则
					columns: [

						{
							key: "clients", title: "json数据", type: "textarea",
							autosize: { minRows: 10, maxRows: 10 },
							showWordLimit: true,
						},
					],
					// 表单验证规则
					rules: {


					},
					beforeAction(formData) {
						try {
							let clients = JSON.parse(formData.clients);
							clients = clients.map(x => { return { name: x.name } })
							return {
								clients
							};
						} catch (err) {
							vk.toast("json解析失败", "none");
							console.error(err);
							return false;
						}
					},
					// add 代表添加 update 代表修改
					formType: "",
					// 是否显示表单的弹窗
					show: false
				}
			},
			// 其他弹窗表单
			formDatas: {},
			// 表单相关结束 -----------------------------------------------------------
		};
	},
	// 监听 - 页面每次【加载时】执行(如：前进)
	onLoad(options = {}) {
		that = this;
		vk = that.vk;
		that.options = options;
		that.init(options);
	},
	onUnload() {
		// 返回false阻止页面被销毁
		return false;
	},
	// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
	onReady() { },
	// 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
	onShow() { },
	// 监听 - 页面每次【隐藏时】执行(如：返回)
	onHide() { },
	// 函数
	methods: {
		// 页面数据初始化函数
		init(options) {
			originalForms["form1"] = vk.pubfn.copyObject(that.form1);
			vk.callFunction({
				url: 'admin/base/organization/sys/getflat',
				data: {
					page: 1,
					pageSize: -1
				},
				success: (data) => {
					this.organizations = data.rows;
				}
			});
		},
		organizationfomattr(val, row, column, index) {
			// console.log(this.categories.find(x=>x._id==val))
			return this.organizations.find(x => x._id == val).name;

		},
		userInfoFomattr(val, row, column, index) {

			if (this.$fn.isNotNull(val))
				return val.map(x => x.nickname).join(",");
			else
				return ''
		},
		// 页面跳转
		pageTo(path) {
			vk.navigateTo(path);
		},
		// 表单重置
		resetForm() {
			vk.pubfn.resetForm(originalForms, that);
		},
		// 搜索
		search() {
			that.$refs.table1.search();
		},
		// 刷新
		refresh() {
			that.$refs.table1.refresh();
		},
		// 获取当前选中的行的数据
		getCurrentRow() {
			return that.$refs.table1.getCurrentRow();
		},
		// 监听 - 行的选中高亮事件
		currentChange(val) {
			that.table1.selectItem = val;
		},
		// 当选择项发生变化时会触发该事件
		selectionChange(list) {
			that.table1.multipleSelection = list;
		},
		// 显示添加页面
		addBtn() {
			that.resetForm();
			that.form1.props.action = 'admin/base/client/sys/add';
			that.form1.props.formType = 'add';
			that.form1.props.title = '添加';
			that.form1.props.show = true;
			let _detection_categorys;
			vk.callFunction({
				url: 'admin/base/detection_category/sys/getList',
				data: {
					page: 1,
					pageSize: -1
				},
				success: (data) => {
					_detection_categorys = data.rows;
					that.$set(that.form1.data, 'price_list', _detection_categorys.map(x => { return { category_name: x.name, category_id: x._id, price: 0 } }))

				}
			});
		},
		// 显示修改页面
		async updateBtn({ item }) {
			that.form1.props.action = 'admin/base/client/sys/update';
			that.form1.props.formType = 'update';
			that.form1.props.title = '编辑';
			that.form1.props.show = true;
			that.form1.data = item;
			let _detection_categorys;
			await vk.callFunction({
				url: 'admin/base/detection_category/sys/getList',
				data: {
					page: 1,
					pageSize: -1
				},
				success: (data) => {
					_detection_categorys = data.rows.map(x => { return { category_name: x.name, category_id: x._id, price: 0 } })


				}
			});


			if (this.$fn.isNull(item.price_list)) {

				that.$set(item, 'price_list', _detection_categorys)
			} else {
				that.$set(item, 'price_list', unionBy(item.price_list, _detection_categorys, 'category_id'))
			}
		},
		// 删除按钮
		deleteBtn({ item, deleteFn }) {
			deleteFn({
				action: "admin/base/client/sys/delete",
				data: {
					_id: item._id
				},
			});
		},
		// 监听 - 批量操作的按钮点击事件
		batchBtn(index) {
			switch (index) {
				case 1: vk.toast("批量操作按钮1"); break;
				case 2: vk.toast("批量操作按钮2"); break;
				default: break;
			}
		},
		addByJsonBtn() {
			that.form2.props.action = 'admin/base/client/sys/addbatch';
			that.form2.props.formType = 'update';
			that.form2.props.title = '编辑';
			that.form2.props.show = true;
		}
	},
	// 监听属性
	watch: {

	},
	// 过滤器
	filters: {

	},
	// 计算属性
	computed: {

	}
};
</script>

<style lang="scss" scoped>
.page-body {}
</style>
