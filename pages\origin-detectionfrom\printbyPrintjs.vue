<template>
    <vk-data-dialog v-model="value.show" :title="page.title" :top="page.top" :width="page.width" mode="form"
        @open="onOpen" @closed="onClose" :close-on-click-modal="true">
        <view :id="`tableId`">
            <view style="padding: 20px;width: 350px;color: black;">
                <view style="display: flex;justify-content: flex-start;align-items: center;">
                    <view style="font-size: 18px;font-weight: 600;width: 200px;">{{ value.data.supplier_name }}</view>
                    <view style="display: flex;flex-direction: column;align-items: center;">
                        <vue-qr
                            :text="`https://static-host-uebpmzce-web.gzg.sealos.run/#/pages/qr_info/o?id=${value.data._id}`"
                            :margin="3" :size="150"></vue-qr>
                        <view style=" font-size: 17px;font-weight: 600;">{{ value.data.samplename }}</view>
                    </view>
                </view>

                <view style="text-align: center;font-size: 16px;">
                    深圳市金阅检测科技有限责任公司
                </view>
            </view>
        </view>
        <template v-slot:footer="{ close }">
            <!--这里是底部插槽-->
            <el-button @click="print2">打 印</el-button>
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="close">确 定</el-button>
        </template>
    </vk-data-dialog>
</template>

<script>
import {
    getLodop
} from '@/common/function/LodopFuncs.js'
import VueQr from 'vue-qr';
import Print from 'print-js'
var that; // 当前页面对象
var vk = uni.vk; // vk实例
export default {
    components: {
        VueQr
    },
    props: {
        value: {
            Type: Object,
            default: function () {
                return {
                    show: false,
                    data: {}
                };
            }
        },
    },
    data: function () {
        // 组件创建时,进行数据初始化
        return {
            page: {
                title: "标题",
                submitText: "确定",
                cancelText: "关闭",
                showCancel: true,
                top: "3vh",
                width: "806px",

            },

            logourl: require('@/static/logo.png')
        };
    },
    mounted() {
        that = this;

        that.init();
    },
    methods: {
        // 初始化
        init() {
            let { value } = that;



            //that.$emit("input", value);
        },
        handleImport() {
            this.$refs.file.click()
        },
        // 监听 - 页面打开
        onOpen() {

            that = this;
            let { item = {} } = that.value;
            that.form1.props.show = true;

        },
        // 监听 - 页面关闭
        onClose() {
            that.$refs.form1.resetForm(); // 关闭时，重置表单
        },
        // 监听 - 提交成功后
        onFormSuccess() {
            that.value.show = false; // 关闭页面
            that.$emit("success");
        },
        print() {
            const LODOP = getLodop()
            try {
                if (LODOP.VERSION) {
                    LODOP.PRINT_INIT("检测单打印");
                    LODOP.SET_PRINT_PAGESIZE(3, 300, 500, '')  //设置横向


                    var strBodyStyle = '<style>'
                    strBodyStyle += 'table { border-top: 1 solid #000000; border-left: 1 solid #000000;  border-collapse:collapse;  border-spacing:0;}'
                    strBodyStyle += 'caption {  line-height:2em; }'
                    strBodyStyle += 'td { border-right: 1 solid #000000; border-bottom: 1 solid #000000; text-align:center; padding:2px 3px; font-size:11px;}'
                    strBodyStyle += '</style>' //设置打印样式
                    var strFormHtml = strBodyStyle + '<body>' + document.getElementById(`tableId`).innerHTML + '</body>'   //获取打印内容

                    LODOP.ADD_PRINT_HTM(0, 0, "90%", "90%", strFormHtml);
                    LODOP.SET_PRINT_STYLEA(0, "Vorient", 4);
                    LODOP.SET_PRINT_MODE("CATCH_PRINT_STATUS", true);
                    LODOP.NewPageA()
                    LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW", true);

                    LODOP.SET_PRINT_MODE("RESELECT_PAGESIZE", true)
                    // LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED", 1);//横向时的正向显示


                    LODOP.PREVIEW()
                }
            } catch (error) {
                window.open('https://mp-5fe77170-8511-415d-8f6e-02d647d2979a.cdn.bspapp.com/print/CLodop_Setup_for_Win32NT.exe');
            }

        },
        print2() {

            printJS({
                printable: `tableId`, // 'printFrom', // 标签元素id
                type: 'html',
                header: '', // '表单',
                targetStyles: ['*'],
                style: '@page {margin:1 1mm};@media print{@page {size:landscape}}', // 可选-打印时去掉眉页眉尾
                ignoreElements: [], // ['no-print']
                properties: null,
                landscape: true, // 设置为横向布局
            })
        },
        // 根据 sample_list 长度计算行高
        calcRowHeight(length) {
            return length < 20 ? 30 : 20;
        },
        // 根据 sample_list 长度计算字体大小
        calcFontSize(length) {
            return length < 20 ? 14 : 10;
        }
    },
    // 监听属性
    watch: {

    },
    // 计算属性
    computed: {

    }
};
</script>

<style lang="scss" scoped>
.daying {
    border-spacing: 0;
    width: 100%;
    border-collapse: collapse;
    font-family: 宋体;
    color: #000
}

.daying td {
    border: 1px solid #000;
    height: 15px
}

.daying td label {
    font-size: 14px
}

.daying td span {
    font-size: 16px
}

.page-footer {
    position: fixed;
    bottom: 0;
    width: 100%
}


// .print_content {
//     -webkit-print-color-adjust: exact;
//     -moz-print-color-adjust: exact;
//     -ms-print-color-adjust: exact;
//     print-color-adjust: exact
// }</style>
