<template>

    <div class="page" style="overflow-x: scroll;">
        <div style="width: 100%; align-content: center;">
       
                    <div style="z-index: -1;height: 1250px; display: flex; -ms-flex-align: center; align-items: center; -ms-flex-pack: center; justify-content: start; -ms-flex-direction: column; flex-direction: column"
                        >
                        <!--!封面页开始-->
                        <!-- <div style="position: relative; height: 1200px;width:850px;padding: 50px;display: flex;flex-direction: column;
                        align-items: center;font-family: 宋体;color: black;font-size: 20px;">
                            <div style="display: flex;flex-direction: column;width: 100%;font-size: 16px; ">
                                <div style="text-align: right;">报告编号：{{ formdata.no }}</div>
                                <div style="text-align: right;display: flex;justify-content: end;">
                                    <div style="width: 120px;;font-size: 15px;">
                                        <span style="display: ruby-text">
                                            {{ `第${1}页 / 共1页` }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div style="font-size: 35px;font-weight: 600;margin-top: 50px;">检测报告</div>

                            <div style="display: flex;flex-direction: column;margin-top: 300px;">
                                <div style="display: flex;margin-top: 15px;">
                                    <div style="width: 100px;">样品名称：</div>
                                    <div style="width: 300px;border-bottom: 0.5px solid;text-align: center;">{{
                                        formdata.samplename }}</div>
                                </div>
                                <div style="display: flex;margin-top: 15px">
                                    <div style="width: 100px;">委托单位：</div>
                                    <div style="width: 300px;border-bottom: 0.5px solid;text-align: center;">{{
                                        formdata.client_name }}</div>
                                </div>
                                <div style="display: flex;margin-top: 15px;">
                                    <div style="width: 100px;">检测类别：</div>
                                    <div style="width: 300px;border-bottom: 0.5px solid;text-align: center;">{{
                                        formdata.detection_type }}</div>
                                </div>
                                <div style="display: flex;margin-top: 15px;">
                                    <div style="width: 100px;">检验方式：</div>
                                    <div style="width: 300px;border-bottom: 0.5px solid;text-align: center;">快速检测
                                    </div>
                                </div>
                            </div>
                            <div
                                style="position: absolute;bottom:10%;display: flex;flex-direction: column;align-items: center;">
                                <div style="font-size: 22px;font-weight: 600;">深圳市金阅检测科技有限责任公司</div>
                                <div style="margin-top: 18px;">{{ vk.pubfn.timeFormat(formdata.detect_time,
                                    'yyyy年MM月dd日') }}</div>
                            </div>
                        </div> -->
                        <!--!封面页结束-->
                        <div>
                            <div style="display: flex; width: 100%; font-family: 宋体; margin-top: 20px">
                                <div style="width: 185px">
                                    <img src="@/static/print-log.png" height="80px" width="170px" />
                                </div>
                                <div style="display: flex; flex-direction: column; justify-content: center">
                                    <div>
                                        <span
                                            style="font-size: 30px; color: black; display: ruby-text; font-weight: bold; line-height: 34px; flex-wrap: nowrap; width: 100%">深圳市金阅检测科技有限责任公司</span>
                                    </div>
                                    <div style="color: black; font-size: 23px; font-weight: bold; display: ruby-text">
                                        Shenzhen King Eye Testing Technology Co., Ltd</div>
                                </div>
                            </div>
                        </div>
                        <div
                            style="width: 95%; padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px">
                        </div>
                        <div
                            style="width: 95%; padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px">
                        </div>
                        <div
                            style="position: relative;display: flex;justify-content: center; width: 95%; font-family: 宋体">
                            <div
                                style="font-size: 30px !important; font-weight: bold; font-weight: bold; color: black; margin-top: 20px; text-align: center">
                                检 测 报 告</div>
                        </div>
                        <div style="width: 95%">
                            <div style="margin-bottom: 5px; display: flex; justify-content: space-between">
                                <div style="display: flex; justify-content: start">
                                    <span
                                        style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">报告编号：</span>
                                    <span style="font-size: 16px; font-family: 宋体; color: black">{{ formdata.no }}</span>
                                </div>

                    
                            </div>
                            <div>
                                     <table class="daying" style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr>
                                        <td colspan="4" align="center" style="height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">客户信息及检测信息</span></td>
                                        <td rowspan="6" class="qrcode-cell" style="border: 1px solid #000; width: 20%">
                                            <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%">
                                                <vue-qr :logoSrc="logourl" :margin="2" :text="`https://static-host-uebpmzce-web.gzg.sealos.run/#/pages/qr_info/d?no=${ formdata.no}`" :size="120"></vue-qr>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">委托单位名称</span></td>
                                        <td align="center" colspan="3" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{  formdata.client_name }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">委托单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{  formdata.address || '广东省佛山市三水区耀华路8号' }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受 检 单 位</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{  formdata.submitUser }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受检单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{  formdata.submituser_address || formdata.addressps||'' }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">接 收 日 期</span></td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat( formdata.detect_time, 'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样 品 状 态</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">鲜样正常</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">验 证 日 期</span></td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat( formdata.detect_time, 'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检 测 类 型</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">委托检测</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 12px">扫描二维码辨别报告真伪</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div
                            style="align-items: center; font-size: 18px; font-weight: bold; font-weight: bold; margin-top: 6px; margin-bottom: 6px; font-family: 宋体; color: black">
                            检 测 结 果</div>
                        <div style="width: 95%">
                            <div>
                                <table class="daying"
                                    style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr style="height: 30px">
                                        <td align="center" width="5%" style="border: 1px solid #000; height: 15px"><span
                                                style="font-weight: bold; font-size: 16px; font-family: 黑体">序号</span>
                                        </td>
                                        <td align="center" width="12%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品编号</span>
                                        </td>
                                        <td align="center" width="12%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品名称</span>
                                        </td>
                                        <td align="center" colspan="2" width="16%"
                                            style="border: 1px solid #000; height: 15px"><span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测项目</span>
                                        </td>
                                        <td align="center" width="12%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">单位</span>
                                        </td>

                                        <td align="center" width="12%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检出限</span>
                                        </td>
                           
                                        <td align="center" width="11%" style="border: 1px solid #000; height: 15px">
                                            <span
                                                style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">单项判定</span>
                                        </td>
                                    </tr>

                                    <!-- 动态行高和字体大小 -->
                                    <tr v-for="(detail, index) in  formdata.sample_list" :key="index"
                                        :style="{ height: calcRowHeight( formdata.sample_list.length) + 'px', fontSize: calcFontSize( formdata.sample_list.length) + 'px' }">
                                        <td align="center" style="border: 1px solid #000; height: 15px">{{ index + 1 }}
                                        </td>
                                        <td align="center" v-if="index == 0" :rowspan=" formdata.sample_list.length"
                                            style="border: 1px solid #000; height: 15px">{{
                                            detail.sampleno }}</td>
                                        <td align="center" v-if="index == 0" :rowspan=" formdata.sample_list.length"
                                            style="border: 1px solid #000; height: 15px">{{
                                            detail.samplename }}</td>
                                        <td align="center" colspan="2" style="border: 1px solid #000; height: 15px">{{
                                            detail.detection_category }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 15px"
                                            >
                                            {{ detail.unit||'μg/kg' }}
                                        </td>
                                        <!-- <td align="center" style="border: 1px solid #000; height: 15px">{{
                                            detail.detection_standard }}</td> -->
                                        <td align="center" style="border: 1px solid #000; height: 15px">{{
                                            detail.detection_include }}</td>
                             
                                        <td align="center" style="border: 1px solid #000; height: 15px"
                                            :style="{ 'font-size': '14px' }">
                                            {{ detail.result }}
                                        </td>
                                    </tr>
                                </table>

              
                                <table
                                    style="color: black;width: 100%;  font-size: 12px; font-family: 宋体;border-collapse: collapse;">
                                    <tr>
                                        <td width="12%"
                                            style="border: 1px solid #000; height: 20px;padding-left: 8px;padding-right: 8px;">
                                            检测方法:</td>
                                        <td width="88%"
                                            style="border: 1px solid #000; height: 20px;padding-left: 8px;padding-right: 8px;">
                                            {{ uniq(formdata.sample_list.map(x => x.detection_standard)).join('/') }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td
                                            style="border: 1px solid #000; height: 15px;padding-left: 8px;padding-right: 8px;">
                                            判定依据:</td>
                                        <td
                                            style="border: 1px solid #000; height: 15px;padding-left: 8px;padding-right: 8px;">
                                            KJ 201701、KJ201705、KJ201905、KJ201906 食品中磺胺类的快速检测
                                        </td>

                                    </tr>
                                    <tr>
                                        <td colspan="2"
                                            style="border: 1px solid #000; padding-left: 8px;padding-right: 8px;font-size: 12px;">
                                       <div>{{
                                              formdata.remark|| '磺胺类:磺胺嘧啶 20μg/kg 磺胺甲基嘧啶 20μg/kg 磺胺索嘧啶 20μg/kg 磺胺对甲嘧啶20μg/kg 磺胺氨吡嗪 30μg/kg 磺胺二甲氧密啶20μg/kg 磺胺间甲氧嘧啶20μg/kg 磺胺多辛20μg/kg 磺胺甲噻二唑20μg/kg 磺胺氯哒嗪30μg/kg 磺胺甲氧哒嗪40μg/kg 磺胺二甲基嘧啶70μg/kg 磺胺甲噁唑100μg/kg 磺胶苯酰40μg/kg 磺胶喹恶林80μg/kg 磺胶异噁唑100μg/kg'
                                        }}
                                  
                                       </div>     
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div style="text-align: left; padding: 5px 5px;font-family: 黑体;  color: #606266 ;padding-left: 10px; font-size: 12px;border: 1px solid #000; border-top: none;">1、报告无快检专用章无效；报告无检测人、审核人签名无效，报告经涂改、增删无效；</br>
2、以上样品信息均由客户提供，本司不承担其信息准确性的责任；</br>
3. 本报告只对送检样品检测结果负责。以上检测结果为快速检测定性检测结果，不具备法律效力，仅供客户参考。</br>
4、委托单位对本检测报告有异议，请在收到报告之日或指定领取报告之日起，三个工作日内提出申诉，逾期不子受理。</div>

            <!--?印章-->
                        <div style="background-color: aquamarine; width: 95%; font-size: 16px; font-weight: bold; margin-bottom: 10%; position: relative"
                            :style="{ 'margin-top': formdata.sample_list.length > pageSzie ? '-2px' : '2px' }">
                            <div
                                style="z-index: 999; position: absolute; transform: translateX(-140%) translateY(-50%); margin-left: 100%; background-color: transparent">
                                <img src="@/static/gz.png" width="150px;" height="150px;" />
                            </div>
                            <div
                                style="margin-left: 100%; z-index: 2; white-space: nowrap; position: absolute; transform: translateX(-100%); font-family: 黑体">
                                此处未盖本公司快检专用章，则本报告无效。</div>
                        </div>
                        <!--?印章-->
                        <div
                            style="display: flex; flex-direction: row; justify-content: space-between; width: 95%; z-index: 1">
                            <div
                                style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                                <div>检测人:</div>
                                <div style="position: absolute; left: 28%; top: -80%">
                                    <span>
                                        <img v-if="$fn.isNotNull(formdata.detection_user_info.sign_image)" width="100px"
                                            height="40px" :src="formdata.detection_user_info.sign_image" />
                                    </span>
                                </div>
                            </div>
                            <div
                                style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                                <div>审核人:</div>
                                <div style="position: absolute; left: 28%; top: -60%">
                                    <img src="@/static/shr.png" width="100px" height="40px" />
                                </div>
                            </div>
                            <div style="width: 34%; font-size: 16px; font-weight: bold; font-family: 黑体">
                                <span>签发日期:</span>
                                <span>{{ vk.pubfn.timeFormat(formdata.detect_time, 'yyyy年MM月dd日') }}</span>
                            </div>
                        </div>

  
                        </div>
                         <div style="margin-top: auto; width: 95%;justify-content: center;  padding-bottom: 10px">

                            <div
                                style="padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px">
                            </div>
                            <div
                                style="padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px">
                            </div>
                                                      <div style="display: flex; align-content: space-between; color: #606266; margin-top: 10px">
                                <div style="font-size: 12px; font-family: 黑体; display: ruby-text"> 0755-28380866 深圳市金阅检测科技有限责任公司</div> 
                                <div style="margin-left: 22%; font-size: 12px;text-align: end; font-family: 黑体; align-content: flex-end; color: #606266">地址：深圳市大鹏新区大鹏街道布新社区布新村工业大道2号C401</div>
                            </div>
                            <div
                                style="margin-top: 20px;text-align:center ;width: 100%; margin-bottom: 20px; text-align: center; font-size: 18px">
                                ***报告完结***</div>
                        </div>
                        </div>
         
                                 
                    </div>
             
        </div>

 

</template>

<script>
import VueQr from 'vue-qr';

import { slice, uniq } from 'lodash';
var that; // 当前页面对象
var vk = uni.vk; // vk实例
export default {
    components: {

VueQr
    },
    props: {
        value: {
            Type: Object,
            default: function () {
                return {
                    show: false,
                    printData: []
                };
            }
        }
    },
    data: function () {
        // 组件创建时,进行数据初始化
        return {

             formdata: {

            },
             count: 0,
            logourl: require('@/static/logo.png'),
            pageSzie: 22,
            pageSzie2: 53,
            pageSzie3: 84,
            pageSzie4: 105,
            pageSzie4: 126,
            onePageHeight: 1200
        };
    },
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        that.init(options);
        // console.log(vk.getVuex('$user.userInfo').nickname)
        // this.form1.data.enterprise_name = vk.getVuex('$user.userInfo').nickname
    },
    methods: {
        // 初始化
        init(options) {

            vk.callFunction({
                url: 'client/pub.get_detectionfrom',
                title: '请求中...',
                data: {
                    no: options.no
                },
                success: (data) => {
                    that.formdata = data.data;
                    that.count = that.formdata.sample_list.length;

       
                }
            });


        },


        // 监听 - 页面关闭
        onClose() { },
 

    
        // 根据 sample_list 长度计算行高
        calcRowHeight(length) {
            // return length < 20 ? 30 : 20;
            return 30;
        },
        // 根据 sample_list 长度计算字体大小
        calcFontSize(length) {
            return 12;
            //  return length < 20 ? 14 : 10;
        },
        getsubList(list, index) {
            if (index == 1) {
                return list.slice(0, this.pageSzie);
            } else if (index == 2) {
                return slice(list, this.pageSzie, this.pageSzie2);
            } else if (index == 3) {
                return slice(list, this.pageSzie2, this.pageSzie3);
            } else if (index == 4) {
                return slice(list, this.pageSzie3, this.pageSzie4);
            }
            else if (index == 5) {
                return slice(list, this.pageSzie4, list.length);
            }
        },
        getSampleSatus(data) {
            let _result = '鲜样正常';

            if (vk.pubfn.isNotNull(data.sample_list)) {
                data.sample_list.forEach(element => {
                    if (vk.pubfn.isNotNull(element.yxresult)) {
                        if (element.yxresult != '阴性') {
                            _result = element.yxresult;
                            return _result;
                        }
                    }
                });
            }
            return _result;
        },
        getPageHeight(v) {
            return 2400;
            let _height = this.onePageHeight;
            if (formdata.sample_list.length < this.pageSzie) {
                return _height;
            } else {
                _height = this.onePageHeight + this.onePageHeight * Math.ceil((formdata.sample_list.length - this.pageSzie) / (this.pageSzie * 1.5));
            }
            //  console.log(_height);
            return _height;
        },
        getPageCount(v) {
            if (formdata.sample_list.length < this.pageSzie) {
                return 1 + 1;
            } else {
                return 2 + Math.ceil((formdata.sample_list.length - this.pageSzie) / (this.pageSzie * 1.5));
            }
        },
        uniq(list) {
            return uniq(list)
        }

    },
    // 监听属性
    watch: {

    },
    // 计算属性
    computed: {

    }
};
</script>

<style lang="scss" scoped>

body {
    overflow-x: scroll !important;
}

.page {
    padding-bottom: 20px;
}
.page-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
}
</style>
