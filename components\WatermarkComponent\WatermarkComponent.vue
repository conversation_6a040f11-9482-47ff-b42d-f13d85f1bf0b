<template>
  <div class="watermark-container">
    <!-- 实际内容 -->
    <div class="content">
      <slot></slot>
    </div>
    
    <!-- 水印层 -->
    <div class="watermark-layer">
      <div 
        v-for="index in watermarkCount" 
        :key="index" 
        class="watermark"
        :style="getWatermarkStyle(index - 1)">
        {{ text }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WatermarkComponent',
  props: {
    text: {
      type: String,
      default: '金阅检测'
    },
    opacity: {
      type: Number,
      default: 0.15
    },
    fontSize: {
      type: String,
      default: '24px'
    },
    color: {
      type: String,
      default: '#000'
    },
    watermarkCount: {
      type: Number,
      default: 8
    },
    zIndex: {
      type: Number,
      default: 1000
    }
  },
  methods: {
    getWatermarkStyle(index) {
      // 计算水印位置，确保均匀分布
      const rows = Math.ceil(Math.sqrt(this.watermarkCount));
      const cols = Math.ceil(this.watermarkCount / rows);
      
      const row = Math.floor(index / cols);
      const col = index % cols;
      
      // 计算百分比位置
      const topPercent = (row * 100 / rows) + 5;
      const leftPercent = (col * 100 / cols) + 5;
      
      return {
        position: 'absolute',
        top: `${topPercent}%`,
        left: `${leftPercent}%`,
        transform: `rotate(${-30}deg)`,
        opacity: this.opacity,
        fontSize: this.fontSize,
        color: this.color,
        fontWeight: 'bold',
        userSelect: 'none',
        whiteSpace: 'nowrap',
        zIndex: this.zIndex
      };
    }
  }
};
</script>

<style scoped>
.watermark-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.content {
  position: relative;
  z-index: 1;
}

.watermark-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.watermark {
  display: block;
  pointer-events: none;
}

/* 确保打印时水印可见 */
@media print {
  .watermark-layer {
    position: fixed;
    height: 100vh;
    width: 100vw;
    top: 0;
    left: 0;
    z-index: 9999;
    page-break-inside: avoid;
  }
  
  .watermark {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
}
</style>