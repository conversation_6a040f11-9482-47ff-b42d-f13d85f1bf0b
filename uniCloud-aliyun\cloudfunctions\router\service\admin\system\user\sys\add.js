const formRules = require("../util/formRules.js");
module.exports = {
	/**
	 * 添加用户
	 * @url admin/system/user/sys/add 前端调用的url参数地址
	 * data 请求参数 说明
	 * @param {String} username 		用户名
	 * @param {String} nickname 		昵称
	 * @param {String} gender 			性别
	 * @param {Boolean} mobile 		手机号
	 * @param {String} comment 		备注
	 * @param {Array} dcloud_appid 	允许登录的应用列表
	 * @param {Number} login_appid_type 	0:全部应用 1:部分应用
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: '' };
		// 业务逻辑开始-----------------------------------------------------------
		let {
			username,
			password,
			nickname,
			gender,
			mobile,
			email,
			comment,
			organization_id,
			job,
			delivery_company,
			price_list,
			isPriceAll,
			allow_login_background,
			sign_image,
			reviewer_sign_image,
			dcloud_appid = [],
			login_appid_type
		} = data;
		if (!password) password = "234567";
		// 参数合法校验开始-----------------------------------------------------------
		let formRulesRes = await formRules.add(event);
		if (formRulesRes.code !== 0) {
			return formRulesRes;
		}
		// 参数合法校验结束-----------------------------------------------------------

		let dbName = "uni-id-users";
		// 检测username
		let num = await vk.baseDao.count({ dbName, whereJson: { username } });
		if (num > 0) return { code: -1, msg: `用户名【${username}】已注册!` };
		// 检测mobile
		if (mobile) {
			let num = await vk.baseDao.count({ dbName, whereJson: { mobile } });
			if (num > 0) return { code: -1, msg: `手机号【${mobile}】已注册!` };
		}
		// 检测email
		if (email) {
			let num = await vk.baseDao.count({ dbName, whereJson: { email } });
			if (num > 0) return { code: -1, msg: `邮箱【${email}】已注册!` };
		}
		if (typeof uniID.addUser !== "function") return { code: -1, msg: `请升级uni-id版本至3.3.14或以上。` };
		let addUserRes = await uniID.addUser({
			username,
			password,
			mobile,
			email,
			organization_id,
			job,
			price_list,
			delivery_company,
			authorizedApp: dcloud_appid
		});
		if (addUserRes.code !== 0 || !addUserRes.uid) {
			return addUserRes;
		}
		
		try {
			// 判断是否需要设置邀请码
			let autoSetInviteCode = vk.pubfn.getUniIdConfig(config, "autoSetInviteCode");
			if (autoSetInviteCode) {
				uniID.setUserInviteCode({
					uid: addUserRes.uid
				});
			}
		} catch(err){}
		
		// 用户其他信息
		let dataJson = {
			nickname,
			gender,
			comment,
			sign_image,
			reviewer_sign_image,
			allow_login_background:true,
			status: 0,
		};
		// 如果设置允许登录所有应用，则直接删除dcloud_appid字段
		if (login_appid_type === 0) {
			dataJson["dcloud_appid"] = _.remove();
		}
		// 设置用户其他信息
		await vk.baseDao.update({
			dbName,
			whereJson: {
				_id: addUserRes.uid
			},
			dataJson
		});
		res.type = "register";
		res.uid = addUserRes.uid;

		if (isPriceAll && vk.pubfn.isNotNull(delivery_company) && delivery_company.length > 0 && vk.pubfn.isNotNull(price_list) && price_list.length > 0) {

			//删除相关的
			await vk.baseDao.del({
				dbName: "tm-client-price",
				whereJson: {
					client_id: _.in(delivery_company)
				}
			});
			for (let index = 0; index < delivery_company.length; index++) {
				const _clientid = delivery_company[index];
				let _client = await vk.baseDao.findById({
					dbName: "tm-client",
					id: _clientid
				});

				if (vk.pubfn.isNotNull(_client)) {
					let _add_list = price_list.map(x => {
						if (vk.pubfn.isNull(x.client_id)) {
							return {

								'client_id': _client._id,
								'client_name': _client.name,
								...x
							}
						} else
							return x;

					})
					if (vk.pubfn.isNotNull(_add_list)) {
						await vk.baseDao.adds({
							dbName: "tm-client-price",// 表名
							dataJson: _add_list
						});
					}
				}

			}


		}


		return res;
	}
}
