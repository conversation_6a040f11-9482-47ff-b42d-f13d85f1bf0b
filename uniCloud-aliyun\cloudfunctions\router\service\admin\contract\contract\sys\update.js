module.exports = {
	/**
	 * 修改数据
	 * @url admin/kong/sys/update 前端调用的url参数地址
	 * data 请求参数 说明
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: 'ok' };
		// 业务逻辑开始-----------------------------------------------------------
		let {
			_id,
			sort,
			client,
			name,
			client_name,
			contact_name,
			mobile,
			address,
			check_range,
			isHasImg,
			report_send_type,
			pay_type,
			sampleHandling,
			IsreportDetermined,
			check_type,
			isSub,
			detectionFrom,
			isAgreementCustomers,
			contractNumber,
			inspections,
			invoicing,
			originfile,
			invoicefile,
			discount,
			totalSettlement,
			realtotal,
			sample_list
		} = data;
		// 这里需要把 params1 params2 params3 改成你数据库里允许用户添加的字段
		if (vk.pubfn.isNullOne(_id)) {
			return { code: -1, msg: '参数错误' };
		}
		let dbName = "tm-contract";
		await vk.baseDao.updateById({
			dbName,
			id: _id,
			dataJson: {
				sort,
				name,
				client,
				client_name,
				contact_name,
				mobile,
				address,
				check_range,
				isHasImg,
				report_send_type,
				pay_type,
				sampleHandling,
				IsreportDetermined,
				check_type,
				isSub,
				detectionFrom,
				isAgreementCustomers,
				contractNumber,
				inspections,
				invoicing,
				originfile,
				invoicefile,
				discount,
				totalSettlement,
				realtotal,
			}
		});
		let _dels = sample_list.filter(x => x.contract_id == _id).map(x => x._id)
		await vk.baseDao.del({
			dbName: "tm-contract-sample-testing",
			whereJson: {
				_id: _.in(_dels)
			}
		});
		if (sample_list.length > 0) {

			let _add_sample_list = sample_list.map(x => {
				return {
					'contract_id': _id,
					'client_name': client_name,
					...x
				}
			})
			if (vk.pubfn.isNotNull(_add_sample_list)) {
				await vk.baseDao.adds({
					dbName: "tm-contract-sample-testing",// 表名
					dataJson: _add_sample_list
				});
			}

		}





		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}

}
