module.exports = {
  /**
   * 查询多条记录 分页
   * @url admin/kong/sys/getList 前端调用的url参数地址
   * data 请求参数 说明
   * @param {Number}         pageIndex 当前页码
   * @param {Number}         pageSize  每页显示数量
   * @param {Array<Object>}  sortRule  排序规则
   * @param {object}         formData  查询条件数据源
   * @param {Array<Object>}  columns   查询条件规则
   * res 返回参数说明
   * @param {Number}         code      错误码，0表示成功
   * @param {String}         msg       详细信息
   */
  main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk , db, _ } = util;
		let { uid } = data;
		let res = { code : 0, msg : '' };
		// 业务逻辑开始-----------------------------------------------------------
    let dbName = "tm-organization";
    res  = await vk.baseDao.selects({
      dbName: dbName,
      pageIndex: 1,
      pageSize: 500,
      whereJson: {
        // enable: true,
        parent_id: _.in([null, ""]),
        _id: _.exists(true)
      },
      sortArr: [{ name: "sort", type: "asc" }],
      // 树状结构参数
      treeProps: {
        id: "_id",          // 唯一标识字段，默认为 _id
        parent_id: "parent_id", // 父级标识字段，默认为 parent_id
        children: "children",   // 自定义返回的下级字段名，默认为 children
        level: 10,               // 查询返回的树的最大层级。超过设定层级的节点不会返回。默认10级，最大15，最小1
        limit: 500,             // 每一级最大返回的数据。
        // whereJson: {
        //   enable: true
        // }
      }
    });
		return res;
  }

}
