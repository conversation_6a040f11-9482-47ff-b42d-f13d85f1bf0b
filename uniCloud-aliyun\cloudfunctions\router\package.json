{"name": "router", "version": "1.0.0", "description": "在router目录下执行 npm i", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"lodash": "^4.17.21", "uni-id": "file:../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id", "vk-unicloud": "file:../../../uni_modules/vk-unicloud/uniCloud/cloudfunctions/common/vk-unicloud", "uni-config-center": "file:../common/uni-config-center", "uni-id-common": "file:../../../uni_modules/uni-id-common/uniCloud/cloudfunctions/common/uni-id-common"}, "private": true, "cloudfunction-config": {"concurrency": 1, "memorySize": 512, "path": "", "timeout": 60, "triggers": [], "runtime": "Nodejs16", "keepRunningAfterReturn": false}, "extensions": {"uni-cloud-sms": {}}}