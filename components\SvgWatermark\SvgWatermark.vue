<template>
  <div class="inline-watermark-container">
    <div class="content-with-watermarks">
      <slot></slot>
      
      <!-- 内联水印 -->
      <div 
        v-for="index in watermarkCount" 
        :key="index" 
        class="inline-watermark"
        :style="getWatermarkStyle(index)">
        {{ text }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InlineWatermark',
  props: {
    text: {
      type: String,
      default: '金阅检测'
    },
    opacity: {
      type: Number,
      default: 0.15
    },
    fontSize: {
      type: Number,
      default: 24
    },
    color: {
      type: String,
      default: '#000'
    },
    watermarkCount: {
      type: Number,
      default: 8
    }
  },
  methods: {
    getWatermarkStyle(index) {
      // 计算水印位置
      const gridSize = Math.ceil(Math.sqrt(this.watermarkCount));
      const row = Math.floor((index - 1) / gridSize);
      const col = (index - 1) % gridSize;
      
      // 计算位置
      const topPercent = (row * 100 / gridSize) + 5;
      const leftPercent = (col * 100 / gridSize) + 5;
      
      return {
        position: 'absolute',
        top: `${topPercent}%`,
        left: `${leftPercent}%`,
        transform: 'rotate(-30deg)',
        opacity: this.opacity,
        fontSize: `${this.fontSize}px`,
        color: this.color,
        fontWeight: 'bold',
        zIndex: 9999,
        pointerEvents: 'none',
        userSelect: 'none',
        whiteSpace: 'nowrap'
      };
    }
  }
};
</script>

<style>
.inline-watermark-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.content-with-watermarks {
  position: relative;
  width: 100%;
  height: 100%;
}

.inline-watermark {
  position: absolute;
  z-index: 9999;
}

@media print {
  .inline-watermark {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    position: absolute !important;
    z-index: 9999 !important;
  }
}
</style>