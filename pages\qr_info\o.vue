<template>
    <view class="page">
        <view
            style="background-color: #0b92ee; color: white;display: flex;flex-direction: column; justify-content: center;align-items: center;">
            <view class="flex j-c:center ai:center" style="height: 60px;font-size: 20px;text-align: center;">溯源
            </view>
            <view class="my:5">
                商品订单:{{ formdata.sampleno }}
            </view>
            <view class="my:5">
                {{ count }}种商品安全责任溯源中
            </view>
        </view>
        <view class="product-info flex:col my:5 mx:10">
            <view class="ma:5">
                <el-image v-for="p in formdata.goods_info.thumb" style="width: 100px; height: 100px" :src="p"
                    :preview-src-list="formdata.goods_info.thumb" fit="fit"></el-image>
            </view>
            <view class="flex j-c:start my:5">
                <view class="title">商品名称</view>
                <view>{{ formdata.samplename }}</view>
            </view>
            <view class="flex justify-content:start my:5">
                <view class="title">分类信息</view>
                <view>{{ formdata.goods_info.type_name }}</view>
            </view>
            <view class="flex j-c:start my:5">
                <view class="title">商品规格</view>
                <view></view>
            </view>
            <view class="flex j-c:start my:5">
                <view class="title">保质期</view>
                <view></view>
            </view>
        </view>
        <el-tabs class="mx:10" v-model="activeName">
            <el-tab-pane label="商品信息" name="first">
                <view>
                    <view class="f:15 f:#0b92ee font-weight:medium">
                        生产信息
                    </view>
                    <view class="flex j-c:start my:5">
                        <view class="title">采购日期</view>
                        <view>{{ vk.pubfn.timeFormat(formdata.form_info.detect_time,
                            "MM月dd日") }}</view>
                    </view>
                    <view class="flex j-c:start my:5" style=" flex-wrap: nowrap;align-items: start">
                        <view class="title">供应商名称</view>
                        <view class="break-word">{{ formdata.supplier_name }}</view>
                    </view>
                    <view class="flex j-c:start my:5" style=" flex-wrap: nowrap;align-items: start">
                        <view class="title">供应商资质</view>
                        <view>
                            <el-image v-for="p in formdata.supplier_info.thumb" style="width: 100px; height: 100px"
                                :src="p" :preview-src-list="formdata.supplier_info.thumb" fit="fit"></el-image>

                        </view>
                    </view>
                    <view class="mt:10">
                        <view class="f:15 f:#0b92ee font-weight:medium">检测信息</view>
                    </view>
                    <view class="flex j-c:start">
                        <view class="title">送检单位</view>
                        <view>{{ formdata.form_info.client_name }}</view>
                    </view>
                    <view>
                        <view style=" z-index: -1;display: flex;
                        -ms-flex-align: center;
                        align-items: center;
                        -ms-flex-pack: center;
                        justify-content: center;
                        -ms-flex-direction: column;
                        color: red;
                        flex-direction: column;">


                            <view
                                style="width: 100%; display: flex;align-items: center;justify-content: start;  font-family: 宋体;">

                                <view style="width: 80%;font-weight: 600;">
                                    深圳市金阅检测科技有限责任公司检验检测报告
                                </view>
                                <view style="margin-top: 5px;">
                                    <vue-qr :margin="3" :text="`https://example.com?id=${formdata.form_info.no}`"
                                        :size="50"></vue-qr>

                                </view>
                            </view>

                            <table class="daying" style="border-spacing: 0;
                            width: 100%;
                            border-collapse: collapse;
                            font-family: 宋体;
                            color: #000">
                                <tr>
                                    <td align="center" style="width: 15%; "><span>委托单位/人</span>
                                    </td>
                                    <td align="center" style="width: 20%;"><span>{{
                                            formdata.form_info.client_name }}</span></td>
                                    <td align="center" style="width: 15%; "><span>抽/送样者</span>
                                    </td>
                                    <td align="center" style="width: 20%; "><span>{{ formdata.form_info.submitUser
                                            }}</span>
                                    </td>
                                    <td align="center" style="width: 15%; "><span>检测类别</span>
                                    </td>
                                    <td align="center" style="width: 20%; "><span>
                                            {{ formdata.form_info.detection_type }}</span></td>

                                </tr>

                                <tr>
                                    <td align="center"><span>检测项目</span>
                                    </td>
                                    <td align="center"><span>{{
                                            formdata.form_info.detection_standard_name }}</span></td>
                                    <td align="center"><span>检测标准</span>
                                    </td>
                                    <td align="center"><span>{{
                                            formdata.form_info.detection_standard }}</span>
                                    </td>
                                    <td align="center"><span>检测人</span>
                                    </td>
                                    <td align="center"><span>{{
                                            formdata.form_info.detection_user }}</span>
                                    </td>
                                </tr>

                                <tr>
                                    <td align="center"><span>序号</span>
                                    </td>
                                    <td align="center"><span>样品编号</span>
                                    </td>
                                    <td align="center"><span>样品名称</span>
                                    </td>
                                    <td align="center" colspan="2"><span>检测结论</span>
                                    </td>

                                    <td align="center"><span>备注</span>
                                    </td>
                                </tr>

                                <!-- 动态行高和字体大小 -->
                                <tr v-for="(detail, index) in formdata.form_info.sample_list.filter(x => x.sampleno == formdata.sampleno)"
                                    :key="index">
                                    <td align="center">{{ index + 1 }}</td>
                                    <td align="center">{{ detail.sampleno }}</td>
                                    <td align="center">{{ detail.samplename }}</td>
                                    <td align="center" colspan="2">{{ detail.result }}</td>

                                    <td align="center">{{ detail.remark }}</td>

                                </tr>

                            </table>



                            <view class="mt:10">
                                <div
                                    style="display: flex; align-content: space-between; ;color: #606266;margin-top: 10px;">
                                    <div style="font-size: 12px; font-family: 黑体;display: ruby-text;">Hotline
                                        0755-28380866 深圳市金阅检测科技有限责任公司
                                    </div>
                                    <div
                                        style="margin-left: 15%; font-size: 12px; font-family: 黑体; align-content: flex-end;color: #606266;">
                                        地址：深圳市大鹏新区大鹏街道布新社区布新村工业大道2号C401</div>
                                </div>
                                <div style="display: flex;">
                                    <div style="font-size: 10px; font-family: 黑体;color: #606266;">
                                        声明：未经本公司授权，本报告全部或部分复制、私自转让、盗用、冒用、涂改或以其他形式篡改无效；如果对检测结果有异议，请在检测报告完成之日起4小时内向我司书面提出复检申请（附上检测报告原件），逾期不予受理；送样委托检测结果仅对来样负责。
                                    </div>

                                </div>
                            </view>



                        </view>
                    </view>
                </view>
            </el-tab-pane>
            <el-tab-pane label="企业形象" name="second">
                <view v-if="$fn.isNotNull(formdata.form_info.delivery_info)">
                    <view class="flex j-c:start  my:5" style="flex-wrap: nowrap;align-items: start">
                        <view class="title">企业介绍</view>
                        <view class="break-word">{{ formdata.form_info.delivery_info.enterprise_info }}</view>
                    </view>
                    <view class="flex j-c:start my:5" style="flex-wrap: nowrap;align-items: start">
                        <view class="title">企业图片</view>
                        <view> <el-image v-for="p in formdata.form_info.delivery_info.enterprise_image"
                                :preview-src-list="formdata.form_info.delivery_info.enterprise_image"
                                style="width: 100px; height: 100px" :src="p" fit="fit"></el-image></view>
                    </view>
                    <view class="flex j-c:start my:5" style="flex-wrap: nowrap;align-items: start">
                        <view class="title">作业说明</view>
                        <view class="break-word">{{ formdata.form_info.delivery_info.enterprise_info }}</view>
                    </view>
                    <view class="flex j-c:start my:5" style="flex-wrap: nowrap;align-items: start">
                        <view class="title">作业图片</view>
                        <view> <el-image v-for="p in formdata.form_info.delivery_info.job_image"
                                :preview-src-list="formdata.form_info.delivery_info.job_image"
                                style="width: 100px; height: 100px" :src="p" fit="fit"></el-image></view>
                    </view>
                    <view class="flex j-c:start my:5" style="flex-wrap: nowrap;align-items: start">
                        <view class="title">资质图片</view>
                        <view> <el-image v-for="p in formdata.form_info.delivery_info.statement_qualification"
                                style="width: 100px; height: 100px" :src="p"
                                :preview-src-list="formdata.form_info.delivery_info.statement_qualification"
                                fit="fit"></el-image>
                        </view>
                    </view>
                    <view class="flex j-c:start my:5" style="flex-wrap: nowrap;align-items: start">
                        <view class="title">车辆行驶证</view>
                        <view> <el-image style="width: 100px; height: 100px"
                                :src="formdata.form_info.delivery_info.driving_license"
                                :preview-src-list="[formdata.form_info.delivery_info.driving_license]"
                                fit="fit"></el-image>
                        </view>
                    </view>
                    <view class="flex j-c:start my:5" style="flex-wrap: nowrap;align-items: start">
                        <view class="title">配送人员</view>
                        <view> <el-image style="width: 100px; height: 100px"
                                :src="formdata.form_info.delivery_info.person1"
                                :preview-src-list="[formdata.form_info.delivery_info.person1, formdata.form_info.delivery_info.person2]"
                                fit="fit"></el-image>
                        </view>
                        <view> <el-image style="width: 100px; height: 100px"
                                :src="formdata.form_info.delivery_info.person2"
                                :preview-src-list="[formdata.form_info.delivery_info.person1, formdata.form_info.delivery_info.person2]"
                                fit="fit"></el-image>
                        </view>
                    </view>
                </view>
            </el-tab-pane>

        </el-tabs>
    </view>
</template>
<script>
import VueQr from 'vue-qr';
var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
export default {
    components: {
        VueQr
    },
    data() {
        // 页面数据变量
        return {
            activeName: 'first',
            formdata: {

            },
            count: 0
        }
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        that.init(options);
        // console.log(vk.getVuex('$user.userInfo').nickname)
        // this.form1.data.enterprise_name = vk.getVuex('$user.userInfo').nickname
    },
    // 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
    onReady() {

    },
    // 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
    onShow() {


    },
    // 监听 - 页面每次【隐藏时】执行(如：返回)
    onHide() {


    },
    // 函数
    methods: {
        // 页面数据初始化函数
        init(options) {

            vk.callFunction({
                url: 'client/pub.get_origindetectionfrom',
                title: '请求中...',
                data: {
                    _id: options.id
                },
                success: (data) => {
                    this.formdata = data.data;
                    this.count = this.formdata.form_info.sample_list.length;

                    if (this.formdata.form_info.sample_list.length < 10) {
                        const row = 10 - this.formdata.form_info.sample_list.length
                        for (let i = 0; i < row; i++) {
                            this.formdata.form_info.sample_list.push({
                                sampleno: i == 0 ? '以下空白' : ''

                            })
                        }

                    } else if (this.formdata.form_info.sample_list.length > 10 && this.formdata.form_info.sample_list.length < 20) {
                        const row = 20 - this.formdata.form_info.sample_list.length
                        for (let i = 0; i < row; i++) {
                            this.formdata.form_info.sample_list.push({
                                sampleno: i == 0 ? '以下空白' : ''

                            })
                        }
                    } else if (this.formdata.form_info.sample_list.length > 10 && this.formdata.form_info.sample_list.length < 30) {
                        const row = 30 - this.formdata.form_info.sample_list.length
                        for (let i = 0; i < row; i++) {
                            this.formdata.form_info.sample_list.push({
                                sampleno: i == 0 ? '以下空白' : ''

                            })
                        }
                    } else if (this.formdata.form_info.sample_list.length > 10 && this.formdata.form_info.sample_list.length < 40) {
                        const row = 40 - this.formdata.form_info.sample_list.length
                        for (let i = 0; i < row; i++) {
                            this.formdata.form_info.sample_list.push({
                                sampleno: i == 0 ? '以下空白' : ''

                            })
                        }
                    }
                }
            });


        },

    },
    // 过滤器
    filters: {

    },
    // 计算属性
    computed: {

    }
}
</script>
<style lang="scss" scoped>
.page {
    font-size: 14px;

}

.title {
    margin-right: 5px;
    min-width: 80px;
    display: flex;
    align-items: start;
    color: rgb(87, 87, 87)
}

.daying {
    font-size: 12px;
}

.daying tr td {
    font-size: 12px;
    word-break: break-all;
    color: red;
    border: 1px solid red;
}
</style>
