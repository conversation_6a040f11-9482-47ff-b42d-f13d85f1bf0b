<template>
    <view class="page-body">
        <!-- 页面内容开始 -->

        <!-- 表格搜索组件开始 -->
        <vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" size="mini"
            @search="search"></vk-data-table-query>
        <!-- 表格搜索组件结束 -->

        <!-- 自定义按钮区域开始 -->
        <view>
            <el-row>
                <el-button type="success" size="mini" icon="el-icon-circle-plus-outline" @click="addBtn">添加</el-button>
                <!-- 批量操作 -->
                <el-dropdown v-if="table1.multipleSelection" :split-button="false" trigger="click" @command="batchBtn">
                    <el-button type="danger" size="mini" style="margin-left: 20rpx;"
                        :disabled="table1.multipleSelection.length === 0">
                        批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="1">批量打印</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </el-row>
        </view>
        <!-- 自定义按钮区域结束 -->

        <!-- 表格组件开始 -->
        <vk-data-table ref="table1" :rowNo="true" :border="true" :action="table1.action" :columns="table1.columns"
            :query-form-param="queryForm1" size="mini"
            :right-btns="$hasRole('admin') ? ['update', 'delete'] : ['update']" :selection="true" :pagination="true"
            @update="updateBtn" @delete="deleteBtn" @current-change="currentChange" @selection-change="selectionChange"
            :custom-right-btns="table1.customRightBtns"></vk-data-table>
        <!-- 表格组件结束 -->

        <!-- 添加或编辑的弹窗开始 -->
        <vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="1200px" mode="form"
            :close-on-click-modal="false">
            <vk-data-form v-model="form1.data" size="mini" ref="form1" :rules="form1.props.rules" :action="form1.props.action"
                :before-action="form1.props.beforeAction" :form-type="form1.props.formType"
                :columns='form1.props.columns' label-width="120px" @success="form1.props.show = false; refresh();"
                :inline="true" :columnsNumber="3">
                <template v-slot:sample_list="{ form, keyName }">
                    <!-- <el-button type="success" size="mini" icon="el-icon-circle-plus-outline" style="margin: 10px 5px;"
                        @click="insertEvent(-1)">添加样品</el-button> -->
                    <vxe-table ref="xTable" keep-source border size="small" :scroll-y="{ enabled: false }"
                        :data="form[keyName]" :edit-config="{ trigger: 'click', mode: 'cell', }">
                        <vxe-table-column fixed="left" type="seq" width="80"></vxe-table-column>
                        <vxe-table-column field="sampleno" min-width="200" title="样品编号" align="left" width="100">
                        </vxe-table-column>
                        <vxe-table-column field="samplename" min-width="250" title="样品名称" align="left" width="100">

                        </vxe-table-column>
                        <vxe-table-column field="result" title="检测结论" align="left" width="100">
                        </vxe-table-column>
                        <vxe-table-column field="count" min-width="200" title="数量" align="left" width="100"
                            :edit-render="{ name: 'visible', }">
                            <template v-slot:edit="scope">
                                <vxe-input v-model="scope.row.count" type="number"></vxe-input>
                            </template>
                        </vxe-table-column>
                        <vxe-table-column field="unit" title="单位" align="left" width="110"
                            :edit-render="{ name: 'visible', }">
                            <template v-slot:edit="scope">
                                <vk-data-input-select size="small" v-model="scope.row.unit" :filterable="true"
                                    :localdata='goods_spes' placeholder="请选择" :props="{ value: 'name', label: 'name' }"
                                    width="100px"></vk-data-input-select>

                            </template>
                        </vxe-table-column>
                        <vxe-table-column field="thirdparty_orderno" min-width="160" title="订单编号" align="left"
                            width="200" :edit-render="{ name: 'visible', }">
                            <template v-slot:edit="scope">
                                <vxe-input v-model="scope.row.thirdparty_orderno" type="text"></vxe-input>
                            </template>
                        </vxe-table-column>
                        <vxe-table-column field="supplier_name" min-width="200" title="供应商" align="left" width="210"
                            :edit-render="{ name: 'visible', autoselect: true }">
                            <template v-slot:edit="scope">
                                <vk-data-input-select size="small" v-model="scope.row.supplier_id" :filterable="true"
                                    :localdata='supplier' placeholder="请选择" :props="{ value: '_id', label: 'name' }"
                                    width="200px" @change="(val, formData, column, index, option) => {
                                            scope.row.supplier_name = formData.name
                                        }"></vk-data-input-select>
                            </template>
                        </vxe-table-column>
                        <vxe-column title="操作" width="150">
                            <template #default="{ row }">
                                <template>
                                    <view style="display: flex;">
                                        <vxe-button status="success" @click="marksur(row)" size="small">确定</vxe-button>
                                        <!-- <vxe-button type="error" @click="marksur(row)" size="small">复制</vxe-button> -->
                                        <vxe-button type="error" @click="printmy(row)" size="small">打印</vxe-button>
                                    </view>

                                </template>
                            </template>
                        </vxe-column>

                    </vxe-table>
                </template>
            </vk-data-form>
        </vk-data-dialog>
        <!-- 添加或编辑的弹窗结束 -->
        <print v-model="print" />
        <!-- 页面内容结束 -->
    </view>
</template>

<script>
import print from './printbyPrintjs'
import pinyin from 'js-pinyin';
import { remove, concat, unionBy } from 'lodash'

var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
var originalForms = {};						// 表单初始化数据

export default {
    components: {
        print
    },
    data() {
        // 页面数据变量
        return {
            // 页面是否请求中或加载中
            loading: false,
            detection_categorys: [],
            supplier: [],
            clients: [],
            print: {
                show: false,
                data: {}
            },

            // init请求返回的数据
            data: {

            },
            // 表格相关开始 -----------------------------------------------------------
            table1: {
                // 表格数据请求地址
                action: "admin/testing/detection-form/sys/getList",
                // 表格字段显示规则
                columns: [
                    // { key:"_id", title:"id", type:"text", width:220 },

                    { key: "no", title: "检测编号", type: "text", width: 200, sortable: "custom" },
                    { key: "client", title: "委托单位/人", type: "text", width: 200, sortable: "custom", formatter: this.clients_fomattr },
                    { key: "detection_type", title: "检测类别", type: "text", width: 150, sortable: "custom" },
                    { key: "submitUser", title: "抽/送样者", type: "text", width: 150, sortable: "custom" },
                    { key: "detection_category", title: "检测项目", type: "text", width: 150, sortable: "custom", formatter: this.detection_categorys_fomattr },
                    { key: "detection_standard", title: "检测标准", type: "text", width: 150, sortable: "custom" },
                    { key: "detection_user", title: "检测人", type: "text", width: 150, sortable: "custom" },
                    { key: "printcount", title: "打印次数", type: "text", width: 150, sortable: "custom" },
                    { key: "detect_time", title: "填单时间", type: "time", valueFormat: "yyyy年MM月dd日", width: 150, sortable: "custom" },
                    // { key:"_add_time", title:"添加时间", type:"time", width:160, sortable:"custom"  },
                    // { key:"_add_time", title:"距离现在", type:"dateDiff", width:120 },
                ],
                // 多选框选中的值
                multipleSelection: [],
                // 当前高亮的记录
                selectItem: "",
            },
            // 表格相关结束 -----------------------------------------------------------
            // 表单相关开始 -----------------------------------------------------------
            // 查询表单请求数据
            queryForm1: {
                // 查询表单数据源，可在此设置默认值
                formData: {

                },
                // 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
                columns: [
                    { key: "name", title: "配送公司名称", type: "text", width: 160, mode: "%%" },

                ]
            },
            form1: {
                // 表单请求数据，此处可以设置默认值
                data: {

                },
                // 表单属性
                props: {
                    // 表单请求地址
                    action: "",
                    beforeAction: (formData) => {
                        const $table = this.$refs.xTable
                        const { insertRecords, removeRecords, updateRecords } = $table.getRecordset()
                        formData.sample_list = concat(formData.sample_list, insertRecords);
                        removeRecords.forEach(element => {
                            remove(formData.sample_list, x => x.rowNo == element.rowNo);
                        })
                        if (that.$fn.isNull(formData.sample_list)) {
                            vk.alert(`至少包含一条样品检测数据`)
                            return false;
                        }

                        try {
                            let i = 1;
                            formData.sample_list.forEach(element => {
                                if (that.$fn.isNull(element.isSave) || element.isSave == 0) {

                                    throw new Error(`第${i}行数据未确认`)
                                }
                                i++;
                            });
                        } catch (e) {
                            vk.alert(e.message)
                            return false;
                        }
                        // 可在此处修改 formData 后返回 formData，若在此处return false，则表单不触发提交请求。


                        return formData;
                    },
                    // 表单字段显示规则
                    columns: [

                        {
                            key: "client", title: "委托单位/人", type: "remote-select", placeholder: "请选择委托单位/人",
                            action: "admin/base/client/sys/getalllist",
                            props: { list: "rows", value: "_id", label: "name" },
                            showAll: true,
                            disabled: true,
                            actionData: {
                                pageSize: -1
                            },
                            width: 200
                        },
                        {
                            key: "detection_type", title: "检测类别", type: "select", filterable: true, clearable: true, disabled: true,
                            data: [
                                { value: "送检", label: "送检" },
                                { value: "抽检", label: "抽检" },
                                { value: "驻点抽检", label: "驻点抽检" }
                            ],
                            width: 200
                        },

                        { key: "submitUser", title: "抽/送样者", type: "text", disabled: true, width: 150, sortable: "custom" },
                        {
                            key: "detection_category", title: "检测项目", type: "select", filterable: true, clearable: true, disabled: true, placeholder: "请选择检测项目",
                            data: [],
                            props: { value: "_id", label: "name" },

                            watch: ({ value, formData, column, index, option, $set }) => {

                                let _result = that.detection_categorys.find(x => x._id == value)
                                $set("detection_standard_name", that.$fn.isNotNull(_result) ? `${_result.name}` : '');
                                $set("detection_standard", that.$fn.isNotNull(_result) ? `${_result.standard}` : '');
                            }
                        },

                        { key: "detection_standard", title: "检测标准", type: "text", disabled: true, width: 200, sortable: "custom" },
                        { key: "detection_user", title: "检测人", type: "text", disabled: true, width: 200, sortable: "custom" },
                        { key: "sample_list", title: "样品", type: "text" },
                    ],
                    // 表单验证规则
                    rules: {
                        client: [
                            // 必填
                            { required: true, message: "委托单位/人不能为空", trigger: 'change' }
                        ],
                    },
                    // add 代表添加 update 代表修改
                    formType: "",
                    // 是否显示表单的弹窗
                    show: false
                }
            },
            // 其他弹窗表单
            formDatas: {},
            // 表单相关结束 -----------------------------------------------------------
        };
    },
    watch: {
        detection_categorys(newval, oldval) {

            let _obj = that.form1.props.columns.find(x => x.key == 'detection_category')
            that.$set(_obj, 'data', newval)
        },
        goods(newval, oldval) {
            // let _sample_list = that.form1.props.columns.find(x => x.key == 'sample_list')
            // let _goods = _sample_list.columns.find(x => x.key == 'sample_id')
            // that.$set(_goods, 'data', newval)
        }
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        that.init(options);
    },
    onUnload() {
        // 返回false阻止页面被销毁
        return false;
    },
    // 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
    onReady() { },
    // 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
    onShow() { },
    // 监听 - 页面每次【隐藏时】执行(如：返回)
    onHide() { },
    // 函数
    methods: {
        mychange(val, formData, column, index, option) {
            // console.log(formData)
            // console.log(option)
        },
        // 页面数据初始化函数
        init(options) {
            originalForms["form1"] = vk.pubfn.copyObject(that.form1);
            this.queryForm1.formData = options;
            vk.callFunction({
                url: `admin/base/client/sys/getalllist`,
                data: {
                    page: 1,
                    pageSize: -1
                },
                success: (data) => {
                    this.clients = data.rows;

                }
            });
            vk.callFunction({
                url: 'admin/base/detection_category/sys/getList',
                data: {
                    page: 1,
                    pageSize: -1
                },
                success: (data) => {
                    this.detection_categorys = data.rows;

                }
            });
            vk.callFunction({
                url: 'admin/base/goods_spe/sys/getList',
                data: {
                    page: 1,
                    pageSize: -1
                },
                success: (data) => {
                    this.goods_spes = data.rows;

                }
            });



            let delivery_company = undefined;
            if (this.$hasRole('admin')) {

            } else if (this.$hasRole('ROLE_PSGS')) {

                delivery_company = vk.getVuex('$user.userInfo').nickname;
            } else {

                return;
            }
            vk.callFunction({
                url: 'admin/base/supplier/sys/getList',
                data: {
                    columns: [{
                        key: "delivery_company_name",
                        mode: "=",
                        title: "配送公司名称",
                        type: "text",
                    }
                    ],
                    formData: {
                        delivery_company_name: delivery_company,
                    },

                    page: 1,
                    pageSize: -1
                },
                success: (data) => {
                    this.supplier = data.rows;

                }
            });
        },
        clients_fomattr(val, row, column, index) {
            // console.log(this.categories.find(x=>x._id==val))
            return this.clients.find(x => x._id == val).name;

        },
        detection_categorys_fomattr(val, row, column, index) {
            // console.log(this.categories.find(x=>x._id==val))
            return this.detection_categorys.find(x => x._id == val).name;

        },
        // 页面跳转
        pageTo(path) {
            vk.navigateTo(path);
        },
        // 表单重置
        resetForm() {
            vk.pubfn.resetForm(originalForms, that);
        },
        // 搜索
        search() {
            that.$refs.table1.search();
        },
        // 刷新
        refresh() {
            that.$refs.table1.refresh();
        },
        // 获取当前选中的行的数据
        getCurrentRow() {
            return that.$refs.table1.getCurrentRow();
        },
        // 监听 - 行的选中高亮事件
        currentChange(val) {
            that.table1.selectItem = val;
        },
        // 当选择项发生变化时会触发该事件
        selectionChange(list) {
            that.table1.multipleSelection = list;
        },
        // 显示添加页面
        addBtn() {
            that.resetForm();
            that.form1.props.action = 'admin/testing/detection-form/sys/add';
            that.form1.props.formType = 'add';
            that.form1.props.title = '添加';
            that.form1.props.show = true;
        },
        // 显示修改页面
        updateBtn({ item }) {
            that.form1.props.action = 'admin/testing/detection-form/sys/update';
            that.form1.props.formType = 'update';
            that.form1.props.title = '编辑';
            that.form1.props.show = true;
            that.form1.data = item;
        },
        // 删除按钮
        deleteBtn({ item, deleteFn }) {
            deleteFn({
                action: "admin/testing/detection-form/sys/delete",
                data: {
                    _id: item._id
                },
            });
        },
        // 监听 - 批量操作的按钮点击事件
        batchBtn(index) {
            switch (index) {
                case 1: this.mutifiPrint(); break;
                default: break;
            }
        },

        printmy(row) {
            if (this.$fn.isNull(row.is_supplier_save)) {
                vk.alert(`当前行未进行确认`)
                return
            }


            that.print.data = {}
            that.print.show = true;
            that.print.data = vk.pubfn.deepClone(row)
        },
        activeCellMethod({ row, rowIndex }) {

            if (row.isSave != undefined && row.isSave == 1)
                return false;
            else
                return true;
        },

        async removeEvent(row) {

     
            const $table = this.$refs.xTable
            // remove(this.form1.data.sample_list, x => x.rowNo == row.rowNo);
            // $table.loadData(this.form1.data.sample_list)

            $table.remove(row)

        },

        getCurrentDate() {
            const today = new Date();
            const year = today.getFullYear();
            const month = (today.getMonth() + 1).toString().padStart(2, '0');
            const day = today.getDate().toString().padStart(2, '0');
            return `${year}${month}${day}`;
        },
        marksur(row) {
            if (this.$fn.isNull(row.thirdparty_orderno)) {
                vk.alert(`第三方编号不能为空`)
                return
            }


            if (this.$fn.isNull(row.supplier_name)) {
                vk.alert(`供应商名称不能为空`)
                return
            }
            this.$set(row, 'is_supplier_save', true)


            vk.callFunction({
                url: 'admin/testing/tm-sample-testing/sys/update',
                title: '确认中...',
                data: {
                    ...row
                },
                success: (data) => {
                    const $table = that.$refs.xTable
                    const { updateRecords } = $table.getRecordset()
                 
                    that.form1.data.sample_list = unionBy(updateRecords, that.form1.data.sample_list, '_id');
                    that.refresh()
                }
            });
        }
    },

    // 过滤器
    filters: {

    },
    // 计算属性
    computed: {

    }
};
</script>

<style lang="scss" scoped>
.page-body {}

.vxe-input--panel.type--date,
.vxe-input--panel.type--month,
.vxe-input--panel.type--week,
.vxe-input--panel.type--year {
    z-index: 9998 !important;
}

/v-deep/.vxe-select--panel {
    z-index: 9997 !important;
}
</style>
