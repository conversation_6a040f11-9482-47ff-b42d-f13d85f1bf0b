<template>
	<view class="page-body">


		<!-- 页面内容开始 -->

		<!-- 表格搜索组件开始 -->
		<vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns"
			@search="search"></vk-data-table-query>
		<!-- 表格搜索组件结束 -->

		<!-- 自定义按钮区域开始 -->
		<view class="vk-table-button-box">
			<el-button type="success" size="small" icon="el-icon-circle-plus-outline" @click="addBtn">添加</el-button>
			<!-- 批量操作 -->
			<el-dropdown v-if="table1.multipleSelection" :split-button="false" trigger="click" @command="batchBtn">
				<el-button type="danger" size="small" :disabled="table1.multipleSelection.length === 0">
					批量操作<i class="el-icon-arrow-down el-icon--right"></i>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item :command="1">账号批量解冻</el-dropdown-item>
					<el-dropdown-item :command="2">账号批量冻结</el-dropdown-item>`
					<el-dropdown-item :command="3">批量设置可登录的应用</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>

			<el-button type="primary" size="small" icon="el-icon-s-tools" :disabled="!table1.selectItem"
				@click="bindRoleBtn">角色绑定</el-button>
			<el-button type="primary" size="small" icon="el-icon-warning-outline" :disabled="!table1.selectItem"
				@click="resetPasswordBtn">重置密码</el-button>
		</view>
		<!-- 自定义按钮区域结束 -->

		<!-- 表格组件开始 -->
		<vk-data-table ref="table1" :action="table1.action" :columns="table1.columns" :query-form-param="queryForm1"
			:right-btns="['detail_auto', 'update', 'delete']" :selection="true" :row-no="true" size="mini"
			:pagination="true" @update="updateBtn" @delete="deleteBtn" @current-change="currentChange"
			@selection-change="selectionChange">

			<!-- <template v-slot:button1="{ row }">
	
				<vk-data-input-table-select size="mini" v-model="setDeliveryCompany.delivery_company" multiple style="padding: 7px;
    font-size: 12px;" action="admin/base/client/sys/getList" placeholder="设置配送公司" :columns='[
			{ key: "name", title: "公司名称", type: "text", nameKey: true },
			{ key: "_id", title: "公司id", type: "text", idKey: true },
		]'></vk-data-input-table-select>
			</template> -->
		</vk-data-table>
		<!-- 表格组件结束 -->

		<!-- 添加或编辑的弹窗开始 -->
		<vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="900px" top="4vh" mode="form">
			<vk-data-form ref="form1" v-model="form1.data" :rules="form1.props.rules" :action="form1.props.action"
				:form-type="form1.props.formType" :columns='form1.props.columns' size="mini" label-width="150px"
				max-height="700px" @success="form1.props.show = false; refresh();">
				<template v-slot:delivery_company_batch="{ form, keyName }">
					<view style="height: 36px;display: flex;align-items: start;">
						<el-upload style="display:  inline-block;margin-left: 10px;" size="mini" class="upload-demo"
							ref="upload" action="" :file-list="fileList" :auto-upload="false"
							:on-change="handleFileChange" accept=".xlsx,.xls" :limit="1">
							<el-button slot="trigger" size="mini" type="primary">批量增加配送单位</el-button>

						</el-upload>
					</view>
				</template>
				<template v-slot:price_list="{ form, keyName }">
					<vxe-table ref="xTable" keep-source border resizable show-overflow size="small"
						:data="form[keyName]" :edit-config="{ trigger: 'click', mode: 'cell' }">
						<vxe-table-column fixed="left" type="seq" width="80"></vxe-table-column>

						<vxe-table-column field="category_name" min-width="350" title="检测类型" align="left" width="350">

						</vxe-table-column>
						<vxe-table-column field="price" min-width="150" title="单价(元)" align="left" width="150"
							:edit-render="{ name: 'visible', }">
							<template v-slot:edit="scope">
								<vxe-input v-model="scope.row.price" type="number"></vxe-input>
							</template></vxe-table-column>
					</vxe-table>
				</template>
			</vk-data-form>
		</vk-data-dialog>
		<!-- 添加或编辑的弹窗结束 -->

		<!-- 用户角色授权弹窗 -->
		<bindRole v-model="formDatas.bindRole"></bindRole>
		<!-- 重置密码弹窗 -->
		<resetPassword v-model="formDatas.resetPassword"></resetPassword>
		<!-- 批量设置用户允许登录的客户端 -->
		<setAuthorizedAppLogin v-model="formDatas.setAuthorizedAppLogin" @success="refresh"></setAuthorizedAppLogin>

		<!-- 页面内容结束 -->
	</view>
</template>

<script>
import XLSX from 'xlsx'
import { concat, find, union } from 'lodash'
var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
var originalForms = {};						// 表单初始化数据
var genderData = [
	{ value: 1, label: "男" },
	{ value: 2, label: "女" },
	{ value: 0, label: "保密" }
];
var dcloudAppidData = [];

import bindRole from './form/bindRole'
import resetPassword from './form/resetPassword'
import setAuthorizedAppLogin from './form/setAuthorizedAppLogin'
import { includes } from 'lodash';

export default {
	components: {
		bindRole,
		resetPassword,
		setAuthorizedAppLogin
	},
	data() {
		// 页面数据变量
		return {
			// 页面是否请求中或加载中
			loading: false,
			fileList: [],
			roles: [],
			// init请求返回的数据
			data: {

			},
			// 表格相关开始 -----------------------------------------------------------
			table1: {
				// 表格数据请求地址
				action: "admin/system/user/sys/getList",
				// 表格字段显示规则
				columns: [

					{ key: "avatar", title: "头像", type: "avatar", width: 80 },
					{ key: "username", title: "用户名", type: "text", width: 180, defaultValue: '未设置' },
					{ key: "nickname", title: "昵称", type: "text", width: 180, defaultValue: '未设置' },
					{ key: "mobile", title: "手机号", type: "text", width: 120, defaultValue: "未绑定" },

					{ key: "role", title: "角色", type: "text", width: 120, formatter: this.roleFomattr },
					{ key: "comment", title: "备注", type: "text", width: 160 },

					{
						key: "status", title: "账户状态", type: "tag", width: 120, defaultValue: 0, sortable: "custom",
						data: [
							{ value: 0, label: "正常", tagType: "success" },
							{ value: 1, label: "冻结", tagType: "danger" },
							{ value: 2, label: "审核中", tagType: "primary" },
							{ value: 3, label: "审核拒绝", tagType: "info" }
						]
					},
					{
						key: "gender", title: "性别", type: "radio", width: 80, defaultValue: 0, sortable: "custom",
						data: genderData
					},
					{ key: "register_date", title: "注册时间", type: "time", width: 160, sortable: "custom" },
					{ key: "last_login_date", title: "最后登录时间", type: "dateDiff", width: 130, defaultValue: '从未登录过', sortable: "custom" },
					{ key: "last_login_ip", title: "最后登录ip", type: "text", width: 120, defaultValue: '从未登录过' },
					{ key: "_id", title: "id", type: "text", width: 280 },
				],

				// 多选框选中的值
				multipleSelection: [],
				// 当前高亮的记录
				selectItem: "",
			},
			// 表格相关结束 -----------------------------------------------------------
			// 表单相关开始-----------------------------------------------------------
			// 查询表单请求数据
			queryForm1: {
				// 查询表单数据源，可在此设置默认值
				formData: {
					dcloud_appid: ""
					//allow_login_background : true,
				},
				// 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
				columns: [

					{ key: "username", title: "用户名", type: "text", width: 160, mode: "%%" },
					{ key: "nickname", title: "昵称", type: "text", width: 140, mode: "%%" },
					{ key: "mobile", title: "手机号", type: "text", width: 140, mode: "%%" },
					{ key: "_id", title: "ID", type: "text", width: 140, mode: "=" },


				]
			},
			form1: {
				// 表单请求数据，此处可以设置默认值
				data: {
					gender: 0,
					login_appid_type: 0,
					allow_login_background: true
				},
				// 表单属性
				props: {
					// 表单请求地址
					action: "",
					// 表单字段显示规则
					columns: [
						{ key: "username", title: "用户名", type: "text", show: ["add"] },
						{ key: "nickname", title: "昵称", type: "text" },
						{
							key: "gender", title: "性别", type: "radio",
							data: genderData
						},
						{ key: "password", title: "密码", type: "text", tips: "若密码为空，则默认为234567", show: ["add"] },
						{ key: "mobile", title: "手机号", type: "text" },

						{
							key: "organization_id", title: "组织架构", type: "tree-select", width: 500,
							action: "admin/base/organization/sys/getList",
							props: { list: "rows", value: "_id", label: "name", children: "children" },
						},
						{ key: "job", title: "职务", type: "text" },
						{ key: "sign_image", title: "检测员签名", type: "image", limit: 1, },
						{ key: "reviewer_sign_image", title: "审核人签名", type: "image", limit: 1, tips: "打印时如果没有此值则使用默认的审核人图片，有则使用此值" },
						{ key: "delivery_company_batch", title: "批量导入", type: "text", limit: 1, },
						{
							key: "delivery_company", title: "配送公司",
							type: "table-select", placeholder: "请选择配送公司", multiple: true,
							action: "admin/base/client/sys/getsingelList",
							columns: [
								{ key: "name", title: "公司名称", type: "text", nameKey: true, width: 500, },
								// { key: "_id", title: "公司id", type: "text", idKey: true },
							]
						},
						{ key: "isPriceAll", title: "修改所有配送单位单价", type: "switch", activeValue: true, inactiveValue: false, tips: '要批量修改检测单价必须开启此项' },
						{ key: "price_list", title: "检测单价", type: "text" },
						// {
						// 	key:"allow_login_background", title:"允许登录后台?", type:"switch",
						// 	tips:"只有同时设置可登录的应用有管理端以及允许登后台，该用户才能登录管理端"
						// },
						{
							key: "comment", title: "备注", type: "textarea",
							maxlength: "99", showWordLimit: true, autosize: { minRows: 2, maxRows: 10 },
						},
					],
					// 表单对应的验证规则
					rules: {
						username: [
							{
								required: true,
								validator: vk.pubfn.validator("username"),
								message: '用户名以字母开头，长度在6~18之间，只能包含字母、数字和下划线',
								trigger: 'blur'
							}
						],
						nickname: [
							{ required: true, message: '昵称为必填字段', trigger: 'blur' },
							{ min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
						],
						password: [
							{ validator: vk.pubfn.validator("password"), message: '密码长度在6~18之间，只能包含字母、数字和下划线', trigger: 'blur' }
						],
						mobile: [
							{ validator: vk.pubfn.validator("mobile"), message: '手机号格式错误', trigger: 'blur' }
						]
					},
					// add 代表添加 update 代表修改
					formType: '',
					// 是否显示表单1 的弹窗
					show: false,
				}
			},
			// 其他表单属性容器(请勿修改)
			formDatas: {


			},
			setDeliveryCompany: {
				show: false,
			}
			// 表单相关结束-----------------------------------------------------------
		};
	},
	// 监听 - 页面每次【加载时】执行(如：前进)
	onLoad(options = {}) {
		that = this;
		vk = that.vk;
		that.options = options;
		that.init(options);
	},
	// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
	onReady() { },
	// 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
	onShow() { },
	// 监听 - 页面每次【隐藏时】执行(如：返回)
	onHide() { },
	// 函数
	methods: {
		// 页面数据初始化函数
		init(options) {
			originalForms["form1"] = vk.pubfn.copyObject(that.form1);
			that.getAppList();
			that.getrole();
		},
		// 获取应用列表
		getAppList(obj) {
			// 请在store/modules/$app.js文件里增加代码 appList: lifeData.$app.appList || []
			vk.callFunction({
				url: 'admin/system/app/sys/getList',
				data: {},
				success: function (data) {
					dcloudAppidData = data.rows;
					let dcloudAppidData1 = vk.pubfn.copyObject(data.rows);
					let dcloudAppidData2 = vk.pubfn.copyObject(data.rows);
					let index1 = vk.pubfn.getListIndex(that.form1.props.columns, "key", "dcloud_appid");
					that.form1.props.columns[index1].data = dcloudAppidData1;
					dcloudAppidData2.unshift({
						appid: "___error___",
						name: "不存在的应用"
					});
					dcloudAppidData2.unshift({
						appid: "___empty-array___",
						name: "未绑定应用"
					});
					dcloudAppidData2.unshift({
						appid: "___non-existent___",
						name: "全部应用"
					});
					let index2 = vk.pubfn.getListIndex(that.queryForm1.columns, "key", "dcloud_appid");
					that.queryForm1.columns[index2].data = dcloudAppidData2;
					let appids = [];
					dcloudAppidData.map((item, index) => {
						appids.push(item.appid);
					});
					that.queryForm1.formData.appids = appids;
				}
			});
		},
		getrole() {
			vk.callFunction({
				url: `admin/system/role/sys/getList`,
				data: {
					page: 1,
					pageSize: -1
				},
				success: (data) => {
					this.roles = data.rows;
				}
			});

		},
		roleFomattr(val, row, column, index) {
			console.log(val);
			if (this.$fn.isNotNull(val)) {
				return this.roles.filter(x => includes(val, x.role_id)).map(x => x.role_name).join(",");

			}
			else
				return ''
		},
		// 页面跳转
		pageTo(path) {
			vk.navigateTo(path);
		},
		// 表单重置
		resetForm() {
			vk.pubfn.resetForm(originalForms, that);
		},
		// 搜索
		search() {
			that.$refs.table1.query();
		},
		// 刷新
		refresh() {
			that.$refs.table1.refresh();
		},
		// 获取当前选中的行的数据
		getCurrentRow(key) {
			return that.$refs.table1.getCurrentRow(key);
		},
		// 监听 - 行的选中高亮事件
		currentChange(val) {
			that.table1.selectItem = val;
		},
		// 当选择项发生变化时会触发该事件
		selectionChange(list) {
			that.table1.multipleSelection = list;
		},
		// 显示添加页面
		addBtn() {
			that.resetForm();
			that.form1.props.action = 'admin/system/user/sys/add';
			that.form1.props.formType = 'add';
			that.form1.props.title = '添加';
			that.form1.props.show = true;
			let _detection_categorys;
			vk.callFunction({
				url: 'admin/base/detection_category/sys/getList',
				data: {
					page: 1,
					pageSize: -1
				},
				success: (data) => {
					_detection_categorys = data.rows;
					that.$set(that.form1.data, 'price_list', _detection_categorys.map(x => { return { category_name: x.name, category_id: x._id, price: 0 } }))

				}
			});
		},
		// 显示修改页面
		async updateBtn({ item }) {
			that.form1.props.action = 'admin/system/user/sys/update';
			that.form1.props.formType = 'update';
			that.form1.props.title = '编辑';
			that.form1.props.show = true;
			item.login_appid_type = typeof item.dcloud_appid === "undefined" ? 0 : 1;
			that.form1.data = item;
			let _detection_categorys;
			await vk.callFunction({
				url: 'admin/base/detection_category/sys/getList',
				data: {
					page: 1,
					pageSize: -1
				},
				success: (data) => {
					_detection_categorys = data.rows.map(x => { return { category_name: x.name, category_id: x._id, price: 0 } })


				}
			});


			if (this.$fn.isNull(item.price_list)) {

				that.$set(item, 'price_list', _detection_categorys)
			} else {
				that.$set(item, 'price_list', unionBy(item.price_list, _detection_categorys, 'category_id'))
			}
		},
		// 删除按钮
		deleteBtn({ item, deleteFn }) {
			deleteFn({
				action: "admin/system/user/sys/delete",
				data: {
					_id: item._id
				}
			});
		},
		// 监听 - 批量操作的按钮点击事件
		batchBtn(index) {
			switch (index) {
				case 1: that.frozen(0); break;
				case 2: that.frozen(1); break;
				case 3: that.setAuthorizedAppLogin(); break;
				default: break;
			}
		},
		// 角色绑定按钮
		bindRoleBtn() {
			let item = that.getCurrentRow(true);
			vk.pubfn.openForm('bindRole', { item });
		},
		// 重置密码按钮
		resetPasswordBtn() {
			let item = that.getCurrentRow(true);
			vk.pubfn.openForm('resetPassword', { item });
		},
		//账户批量冻结/解冻
		frozen(status) {
			let user_ids = [];
			that.table1.multipleSelection.map((item, index) => {
				user_ids.push(item._id);
			});
			vk.callFunction({
				url: 'admin/system/user/sys/batchUpdateStatus',
				title: '请求中...',
				data: {
					user_ids,
					status
				},
				success: function (data) {
					that.$notify({
						message: '批量操作成功!',
						type: 'success'
					});
					that.refresh();
				}
			});
		},
		// 批量设置允许登录的客户端
		setAuthorizedAppLogin() {
			let user_ids = [];
			that.table1.multipleSelection.map((item, index) => {
				user_ids.push(item._id);
			});
			that.formDatas.setAuthorizedAppLogin = {
				show: true,
				item: {
					user_ids,
					dcloudAppidData
				}
			};
		},
		handleFileChange(ss) {

			// 获取选择的文件对象
			const file = ss.raw
			// 创建 FileReader 对象
			const reader = new FileReader()
			let that = this;
			// 定义读取文件完成后的回调函数
			reader.onload = async (e) => {
				// 获取文件的二进制数据
				const data = e.target.result
				// 解析文件数据，获取工作簿对象
				const workbook = XLSX.read(data, { type: 'binary' })
				// 获取工作簿中的第一个工作表名称
				const sheetName = workbook.SheetNames[0]
				// 获取工作簿中的第一个工作表对象
				const sheet = workbook.Sheets[sheetName]
				// 将工作表对象转换为 JSON 格式的数据
				const jsonData = XLSX.utils.sheet_to_json(sheet, { range: 1, header: 'A' })
				// 定义一个空数组，用于存储读取的列的值
				const columnData = []
				const cantImport = []
				vk.callFunction({
					url: `admin/base/client/sys/getalllist`,
					need_user_info: false,
					data: {
						page: 1,
						pageSize: -1
					},
					success: (data) => {
						try {

							let _clients = data.rows;
							let _rowindex = 1;
							jsonData.forEach((row, index) => {

								let _client_name = row['B'].trim();

								let _client = find(_clients, x => x.name == _client_name);

								if (that.$fn.isNull(_client)) {
									cantImport.push(_client_name)
									// console.log(_client);
									// throw new Error(`第${_rowindex}行系统不存在${_client_name}这个委托单位/人,请检查!`)
								}
								if (that.$fn.isNotNull(_client))
									columnData.push(_client._id)
								_rowindex++;
							})
							if (that.$fn.isNull(that.form1.data.delivery_company)) {
								that.$set(that.form1.data, 'delivery_company', [])
							}
							that.$set(that.form1.data, 'delivery_company', union(columnData, that.form1.data.delivery_company))

							vk.alert(`导入成功,但${cantImport.join(',')}无法导入,请手动添加`)
							//	console.log(that.form1.data.delivery_company)

						} catch (error) {
							vk.alert(error.message)
							return;
						}
					}
				});
			}
			this.fileList = [];
			// 读取文件内容
			reader.readAsBinaryString(file)
		},
	},
	// 监听属性
	watch: {

	},
	// 过滤器
	filters: {

	},
	// 计算属性
	computed: {

	}
};
</script>
<style lang="scss" scoped>
/v-deep/.vk-data-input-table-select .select-tag {
	margin-top: 5px;
}
</style>
