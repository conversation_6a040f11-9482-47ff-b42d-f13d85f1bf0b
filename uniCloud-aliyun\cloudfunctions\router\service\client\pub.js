'use strict';
var vk; // 全局vk实例
// 涉及的表名
const dbName = {
	//test: "vk-test", // 测试表
};

var db = uniCloud.database(); // 全局数据库引用
var _ = db.command; // 数据库操作符
var $ = _.aggregate; // 聚合查询操作符
/**
 * 权限注意：访问以下链接查看
 * 文档地址：https://vkdoc.fsq.pub/client/uniCloud/cloudfunctions/cloudObject.html#内置权限
 */
var cloudObject = {
	isCloudObject: true, // 标记为云对象模式
	/**
	 * 请求前处理，主要用于调用方法之前进行预处理，一般用于拦截器、统一的身份验证、参数校验、定义全局对象等。
	 * 文档地址：https://vkdoc.fsq.pub/client/uniCloud/cloudfunctions/cloudObject.html#before-预处理
	 */
	_before: async function () {
		vk = this.vk; // 将vk定义为全局对象
		// let { customUtil, uniID, config, pubFun } = this.getUtil(); // 获取工具包
	},
	/**
	 * 请求后处理，主要用于处理本次调用方法的返回结果或者抛出的错误
	 * 文档地址：https://vkdoc.fsq.pub/client/uniCloud/cloudfunctions/cloudObject.html#after-后处理
	 */
	_after: async function (options) {
		let {
			err,
			res
		} = options;
		if (err) {
			return; // 如果方法抛出错误，直接return;不处理
		}
		return res;
	},
	/**
	 * 获取列表
	 * @url client/pub.js.getList 前端调用的url参数地址
	 */
	getList: async function (data) {
		let res = {
			code: 0,
			msg: ''
		};
		let {
			uid
		} = this.getClientInfo(); // 获取客户端信息
		// 业务逻辑开始-----------------------------------------------------------


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	},

	get_origindetectionfrom: async function (data) {
		let res = {
			code: 0,
			msg: ''
		};
		let { _id } = data;
		let info = await vk.baseDao.selects({
			dbName: "tm-sample-testing",
			getCount: false,
			getMain: true,
			getOne: true,
			pageIndex: 1,
			pageSize: -1,
			// 主表where条件
			whereJson: {
				_id: _id
			},
			foreignDB: [{
				dbName: "tm-goods",
				localKey: "sample_id",
				foreignKey: "_id",
				limit: 1,
				as: "goods_info",
			},
			{
				dbName: "tm-supplier",
				localKey: "supplier_id",
				foreignKey: "_id",
				limit: 1,
				as: "supplier_info",
			},
			{
				dbName: "tm-detection-form",
				localKey: "detectionform_id",
				foreignKey: "_id",
				limit: 1,
				foreignDB: [{
					dbName: "tm-deliveryinfo",
					localKey: "client_name",
					foreignKey: "enterprise_name",
					limit: 1,
					as: "delivery_info",
				},
				{
					dbName: "tm-sample-testing",
					localKey: "_id",
					foreignKey: "detectionform_id",
					limit: 43,
					as: "sample_list",
				}
				],
				as: "form_info",
			},
			]
		})
		res.data = info;
		return res;
	},


	get_detectionfrom: async function (data) {
		let res = {
			code: 0,
			msg: ''
		};
		let { no } = data;
		let info = await vk.baseDao.selects({
			dbName: "tm-detection-form",
			getCount: false,
			getMain: true,
			getOne: true,
			pageIndex: 1,
			pageSize: -1,
			// 主表where条件
			whereJson: {
				no: no
			},
			foreignDB: [

				{
					dbName: "uni-id-users",
					localKey: "detection_user",
					foreignKey: "nickname",
					as: "detection_user_info",
					fieldJson: {
						_id: true,
						nickname: true,
						sign_image: true,
						reviewer_sign_image: true
					},
					limit: 1,
				},
				{
					dbName: "tm-deliveryinfo",
					localKey: "client_name",
					foreignKey: "enterprise_name",
					limit: 1,
					as: "delivery_info",
				},
				{
					dbName: "tm-sample-testing",
					localKey: "_id",
					foreignKey: "detectionform_id",
					limit: 200,
					sortArr: [
						{ name: "orderno", type: "asc" },
						{ name: "sampleno", type: "asc" }
					],
					as: "sample_list",
				}

			]
		})
		res.data = info;
		return res;
	},

	getsysinfo: async function (data) {
		let res = {
			code: 0,
			msg: ''
		};
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = "sys-info";
		res = await vk.baseDao.getTableData({
			getOne: true,
			dbName,
			data
		});
		return res;
	},

	/**
	 * 模板函数
	 * @url client/pub.js.test 前端调用的url参数地址
	 */
	test: async function (data) {
		let res = {
			code: 0,
			msg: ''
		};
		let {
			uid
		} = this.getClientInfo(); // 获取客户端信息
		// 业务逻辑开始-----------------------------------------------------------


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	},
	double: async function (data) {
		let res = {
			code: 0,
			msg: ''
		};

		// 业务逻辑开始-----------------------------------------------------------

		let dbName = "tm-goods";
		res = await vk.baseDao.selects({
			dbName: dbName,
			pageIndex: 1,
			pageSize: -1,
			groupJson: {
				_id: "$name", // _id是分组id（_id:为固定写法，必填属性），这里指按user_id字段进行分组
				name: $.first("$name"),
				id: $.first("$_id"),
				count: $.sum(1), // count记录条数
			},
			lastWhereJson: {
				count: _.gt(1)
			}
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	},
};

module.exports = cloudObject;