<template>
    <vk-data-dialog v-model="value.show" :title="page.title" :top="page.top" :width="page.width" mode="form"
        max-height="800px" width="860px" @open="onOpen" @closed="onClose" :close-on-click-modal="true">
        <div id="tableId-0">
            <div v-for="(v, i) in value.printData" :key="i" style="page-break-after: always;font-family: 宋体; color: #000;">
  
                    <div style="display: flex;justify-content: start;align-items:end;">
                        <img :src="logourl" style="margin: 2px;" alt="HRP Logo" class="logo" />
                        <div
                            style=" font-family: 宋体; color: #000;font-size: 30px;font-weight: 600;margin-left: 40px;padding: 10px 0px;">
                            委托检测服务合同</div>
                    </div>
                    <table class="daying"
                        style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000;font-size: 14px;margin-bottom: 30px;">
                        <tr style="border: 1px solid #000;">
                            <td :rowspan="9 + 4" width="5%" style="width: 5%;border: 1px solid #000;">客户填写（带*部分为必填）
                            </td>
                            <td colspan="11" style="border: 1px solid #000;"><span>委托单编号：{{ contractNumber }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="border: 1px solid #000;font-size: 15px;"> <span>*委托单位名称</span> </td>
                            <td colspan="10" style="border: 1px solid #000;">{{ v.client_name }}</td>
                        </tr>
                        <tr>
                            <td colspan="2" style="border: 1px solid #000;">委托单位地址</td>
                            <td colspan="9" style="border: 1px solid #000;">{{ v.address }}</td>
                        </tr>
                        <tr>
                            <td colspan="2" style="border: 1px solid #000;">联系人</td>
                            <td colspan="3" style="border: 1px solid #000;">{{ v.contact_name }}</td>
                            <td colspan="2" style="border: 1px solid #000;">联系电话</td>
                            <td colspan="4" style="border: 1px solid #000;">{{ contactPhone }}</td>
                        </tr>

                        <tr>
                            <td width="10%" style="border: 1px solid #000;">样品名称</td>
                            <td width="7%" style="border: 1px solid #000;">规格</td>
                            <td width="7%" style="border: 1px solid #000;">商标</td>
                            <td width="7%" style="border: 1px solid #000;">质量等级/类型</td>
                            <td width="7%" style="border: 1px solid #000;">生产日期/批号</td>
                            <td width="7%" style="border: 1px solid #000;">产地</td>
                            <td width="7%" style="border: 1px solid #000;">生产单位</td>
                            <td width="10%" style="border: 1px solid #000;">检测项目和方法</td>
                            <td width="7%" style="border: 1px solid #000;">判定标准</td>
                            <td width="7%" style="border: 1px solid #000;">样品编号</td>
                            <td width="7%" style="border: 1px solid #000;">资质章</td>
                        </tr>
                        <tr :key="index">
                            <td style="border: 1px solid #000;height: 50px;">
                                请见附表
                            </td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                        </tr>
                        <tr :key="index">
                            <td style="border: 1px solid #000;height: 50px;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                        </tr>
                        <tr :key="index">
                            <td style="border: 1px solid #000;height: 50px;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                            <td style="border: 1px solid #000;"></td>
                        </tr>

                        <tr>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    检测周期：

                                    <mycheckbox :value="v.check_range" :options="['标准5-8天', '加急3-5天', '特急1-2天']">
                                    </mycheckbox>
                                </view>
                            </td>

                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    报告内是否含现样品照片：
                                    <mycheckbox :value="v.isSub" :options="['是', '否']"></mycheckbox>
                                </view>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    报告送达方式：

                                    <mycheckbox :value="v.report_send_type" :options="['自取', '邮寄', '传真']"></mycheckbox>
                                </view>
                            </td>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    付款支付方式：
                                    <mycheckbox :value="v.pay_type" :options="['转账', '现金', '其它']"></mycheckbox>
                                </view>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    样品处理：

                                    <mycheckbox :value="v.sampleHandling" :options="['客户取回', '由本实验室销毁']"></mycheckbox>
                                </view>
                            </td>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    检测报告是否判定：
                                    <mycheckbox :value="v.IsreportDetermined" :options="['是', '否']"></mycheckbox>
                                </view>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    检测类型：

                                    <mycheckbox :value="v.check_type" :options="['委托检验', '监督抽检']"></mycheckbox>
                                </view>
                            </td>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    是否同意检测分包：
                                    <mycheckbox :value="v.IsreportDetermined" :options="['是', '否']"></mycheckbox>
                                </view>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="12">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    检测方法：

                                    <mycheckbox :value="v.detectionFrom" :options="['由委托方指定', '由检测方决定', '由双方协商决定']">
                                    </mycheckbox>
                                </view>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">样品寄送地址 及汇款账户</td>
                            <td colspan="10">
                                <span style="padding: 10px;">
                                    企业名称：深圳市金阅检测科技有限责任公司 地址：深圳市大鹏新区大鹏街道布新社区布新村工业大 道2号C401</br>
                                    银行账户：41023600040009466 开户行：中国农业银行深圳大鹏支行</br> 邮政编码： 518119 电话：0755-28380866
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td rowspan="2" colspan="2">费用结算</td>
                            <td colspan="8">
                                <view style="display: flex; justify-content: start; align-items: center">
                                    <mycheckbox :value="v.isAgreementCustomers" :options="['协议客户']" />合同编号
                                </view>
                            </td>
                            <td rowspan="2" colspan="2">

                                <view>
                                    <view> 结算费用合计： </view>
                                    <view> {{`¥${v.totalSettlement} 元`}}</view>
                                </view>



                            </td>
                        </tr>
                        <tr>
                            <td colspan="8">
                                <view style="display: flex; justify-content: start; align-items: center">
                                   {{ ` 常规检测计费：¥${v.totalSettlement}元整 开票方式： ` }}<mycheckbox :value="v.invoicing"
                                        :options="['普通发票', '专用发票']"></mycheckbox>
                                </view>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">客户签名： 日期：
                                </view>
                            </td>
                            <td colspan="6">
                                <view style="display: flex; justify-content: start; align-items: center">受理方签名： 日期：
                                </view>
                            </td>
                        </tr>
                        <tr>

                            <td colspan="12">
                                <view style="display: flex; justify-content: start; align-items: center">是否由业务代理签名： 
                                    <mycheckbox :value="v.IsreportDetermined" :options="['否', '是']"></mycheckbox>
                                    ，业务代理方签名（盖章）： 日期：
                                </view>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="height: 90px;">备注</td>
                            <td colspan="10">

                            </td>
                        </tr>
                    </table>

                    <table
                        style="margin: 10px; border-spacing: 0; width: 98%; border-collapse: collapse; font-family: 宋体; color: #000;margin-top: 50px; border: 1px solid #000;">
                        <tr>
                            <td style="text-align: center;font-size: 28px;font-weight:500 ;padding-top: 30px;">
                                委托检测须知
                            </td>
                        </tr>
                        <tr>
                            <td style="font-size: 15px;padding-bottom: 20px;">
                                填写委托检测服务合同前请务必详细阅读本页内容 ，本协议书由客户方签名或盖章，或业务代理方签名盖章，我
                                公司受理人签名或盖章后生效。双方约定条款如下：</br>
                                1.1 总则 本公司根据所确认的委托人具体指令，以合理审慎的技能提供服务。若无此指令，则根据：</br>
                                1.1.1 本公司的任何标准委托单或标准规格单中的条款 ；</br>
                                1.1.2 任何有关的贸易惯例、作法或实践；</br>
                                1.1.3 本公司认为在技术、操作和财务方面适当的方法；</br>
                                1.2本公司出具的结果报告只反映在工作当时所记录的事实 ，而且限于所收到指令的范围内，若无指令时，则限于所
                                用的本条款1.1中给出的可选择参照的范围。本公司无义务提及或报告特定指示范围或其它适用范围外之任何事实或
                                情况。</br>
                                1.3本公司可委派代理或分包商承担全部或部分服务 ，委托方须授权本公司向代理或分包商提供其所承担服务的全部
                                必要的信息。</br>
                                1.4本公司如收到涉及客户和第三方签订的契约或文件 ，如销售合同、信用证、提单等，这些文件仅供参考，而不扩
                                展或限制经本公司承担的服务范围或职责 。</br>
                                1.5 委托方理解并确认本公司虽提供服务但既不取代委托方或任何第三方的位置 ，也不免除委托方或任何第三方的
                                任何职责，此外也不承担、不减轻、不免除、不承诺解除委托方对任何第三方或任何第三方对委托方的任何责任 。</br>
                                1.6若委托方指令本公司对样品进行留样，所有的样品保留期最长为30天或样品性质允许的最短期限，到期后本公司
                                终止对该样品的任何责任，并将依照内部管理指令对样品进行处置 。若委托方未书面提出本公司对样品进行留样 ，
                                本公司将依照内部管理指令进行处置样品 。</br>
                                1.7取报告前应先交费，未交费者不得领取报告；</br>
                                1.8送样检测，我司仅对送检样品负责；</br>
                                1.9本申请单同时作为取报告凭证，请妥善保管；</br>
                                1.10由委托方指定检测方法时，客户将检测方法注明在检测项目后。报告抬头以申请公司名称及地址为准，如有特
                                殊要求请注明；实验室将依据此表中的信息出具正本报告 ，请确保信息的正确性与准确性，此表中信息仅供检测使
                                用，它用无效。若客户未规定/未填写检测方法，则视为同意本实验室所选用方法。</br>
                                1.11若客户未规定/未填写判定依据，则本实验室不对检测结果进行判定。</br>
                                1.12 报告修改需加收费用50元/份，正本报告于签发之日起30天后不再接受客户提出的修改要求。</br>
                                2.1 客户责任</br>
                                2.1.1 在我方报价和服务前，委托方应提供充分且准确的信息、指令和文件，以便所指令的服务得以实施；</br>
                                2.1.2 对任何委托的样品或实验中包含的任何已知的实际或潜在危险或危害 ，包括但不限于放射性、有毒、有害或
                                爆炸元素或物质、环境污染或中毒的存在和危险，委托方须事先通知本公司。</br>
                                3.1服务的暂停和终止</br>
                                如出现以下情况，本公司有权立即且不承担任何责任地暂停或终止提供服务 ：</br>
                                3.1.1 委托方未履行任何其应尽的职责，而且在通知其过失后十天内委托方不作补救 ；</br>
                                3.1.2委托方存在暂停付款、与债权人做出安排、破产、无力偿付、破产管理或停业在内的任何情况。</br>
                                4.1责任和赔偿</br>
                                4.1.1本公司既不是保险商也不是担保人，不承担该方面的任何责任。委托方若寻求保证不损失或不损害，应该适当
                                投保。</br>
                                4.1.2结果报告是根据委托方或其代表所提供的信息 、文件和样品而出具，并且仅可用于维护委托方的利益。委托方
                                应对其根据结果报告所采取的其认为合适的行为负责 ，对任何根据该结果报告采取或未采取的行动 ，本公司及公司
                                的任何成员、代理或分包商都不为此对委托方或任何第三方承担责任 。对因提供给本公司不清楚、不正确、不完全
                                、误导或虚假信息导致的任何不正确结果 ，本公司及公司的任何成员、代理或分包商也不为此对委托方或任何第三
                                方承担责任。</br>
                                4.1.3对因任何超出本公司控制的原因，包括但不限于委托方未履行其任何责任 ，而直接或间接导致的延期、部分或
                                全部服务不能实施，本公司不承担责任。</br>
                                4.1.4 关于损失、损害或任何类型的索赔，本公司的责任在任何情况下都不超过标的样品的标的服务项目收费的五
                                倍。</br>
                                4.1.5 如有任何索赔，委托方必须在发现所声称证明索赔之事实起 30天内书面通知本公司，且应在自下述日期起的
                                一年内提起诉讼，否则本公司在任何情况下都被免除对损失 、损害或费用的所有索赔的全部责任。</br>
                                （1）本公司执行该产生损害赔偿之服务之日期 ；（2)任何指称未履行服务之原应完成日期 。</br>
                                5.1 其他</br>
                                5.1.1在提供服务的过程中和其后的一年内 ，委托方不得直接或间接诱惑、怂恿或提出聘用本公司雇员，使其离开本
                                公司的职位。</br>
                                5.1.2未经本公司书面授权，不允许以广告宣传为目的使用本公司的名称和注册商标 。</br>
                                5.1.3如该服务条款英文版与中文版存在差异 ，则以中文版为准。</br>
                                6.1 管辖法律、司法权和争端裁决</br>
                                因提供服务产生的所有争端，受中华人民共和国法律管辖和依照中华人民共和国法律解释 ，所有争端应提交给中华
                                人民共和国有管辖权的法院裁决。</br>
                            </td>
                        </tr>
                    </table>
                    <div style="margin-top: 70px;padding: 5px; font-size: 26px;text-align: center;">
                        委托检测服务合同附表
                    </div>
                    <table style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">

                        <tr>
                            <td width="10%" style="border: 1px solid #000;">样品名称</td>
                            <td width="7%" style="border: 1px solid #000;">规格</td>
                            <td width="7%" style="border: 1px solid #000;">商标</td>
                            <td width="7%" style="border: 1px solid #000;">质量等级/类型</td>
                            <td width="7%" style="border: 1px solid #000;">生产日期/批号</td>
                            <td width="7%" style="border: 1px solid #000;">产地</td>
                            <td width="7%" style="border: 1px solid #000;">生产单位</td>
                            <td width="10%" style="border: 1px solid #000;">检测项目和方法</td>
                            <td width="7%" style="border: 1px solid #000;">判定标准</td>
                            <td width="7%" style="border: 1px solid #000;">样品编号</td>
                            <td width="7%" style="border: 1px solid #000;">资质章</td>
                        </tr>
                        <tr v-for="s in v.sample_list" :key="index">
                            <td style="border: 1px solid #000;height: 50px;">{{ s.samplename }}</td>
                            <td style="border: 1px solid #000;">{{ s.specs }}</td>
                            <td style="border: 1px solid #000;">{{ s.trademark }}</td>
                            <td style="border: 1px solid #000;">{{ s.qualityGrade }}</td>
                            <td style="border: 1px solid #000;">{{ s.lotNumber }}</td>
                            <td style="border: 1px solid #000;">{{ s.origin }}</td>
                            <td style="border: 1px solid #000;">{{ s.productUnits }}</td>
                            <td style="border: 1px solid #000;">{{ s.detection_categorynames }}</td>
                            <td style="border: 1px solid #000;">{{ s.criteria }}</td>
                            <td style="border: 1px solid #000;">{{ s.sampleno }}</td>
                            <td style="border: 1px solid #000;">{{ s.zizhizang }}</td>
                        </tr>
                    </table>
           
            </div>
        </div>

        <template v-slot:footer="{ close }">
            <!--这里是底部插槽-->
            <el-button @click="print2">打 印</el-button>
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="close">确 定</el-button>
        </template>
    </vk-data-dialog>
</template>

<script>
import VueQr from 'vue-qr';
import Print from 'print-js';
import mycheckbox from '@/components/print-checkbox.vue';
import { slice } from 'lodash';
var that; // 当前页面对象
var vk = uni.vk; // vk实例
export default {
    components: {
        VueQr,
        mycheckbox
    },
    props: {
        value: {
            Type: Object,
            default: function () {
                return {
                    show: false,
                    printData: []
                };
            }
        }
    },
    data: function () {
        // 组件创建时,进行数据初始化
        return {
            page: {
                title: '合同打印',
                submitText: '确定',
                cancelText: '关闭',
                showCancel: true,
                top: '3vh',
                width: '806px'
            },
            form1: {},
            logourl: require('@/static/print-log.png'),
            contractNumber: 'WT240529A003',
            clientName: '中山市西区铁城初级中学',
            clientAddress: '广东省中山市西区翠景道(南)17号地块之28号',
            contactPerson: '尹红林',
            contactPhone: '13824754113',
            samples: [
                {
                    name: '',
                    specification: '',
                    trademark: '',
                    qualityGrade: '',
                    productionDate: '',
                    origin: '',
                    manufacturer: '',
                    testMethod: '',
                    standard: '',
                    sampleNumber: '',
                    remarks: ''
                }
            ],
            testingPeriod: {
                standard: true,
                express: false,
                urgent: false
            },
            reportIncludesPhoto: { yes: false, no: true },
            reportDeliveryMethod: { self: false, express: true, fax: false },
            paymentMethod: { transfer: true, cash: false, other: false },
            sampleHandling: { return: false, dispose: false },
            reportJudgment: { yes: true, no: false },
            testingType: { entrusted: true, supervisory: false },
            subcontractingAgreement: { yes: true, no: false },
            testingBasis: { clientSpecified: false, labDetermined: true, mutuallyAgreed: false }
        };
    },
    mounted() {
        that = this;

        that.init();
    },
    methods: {
        // 初始化
        init() {
            let { value } = that;

            //that.$emit("input", value);
        },
        handleImport() {
            this.$refs.file.click();
        },
        // 监听 - 页面打开
        onOpen() {
            that = this;
            let { item = {} } = that.value;
            that.form1.props.show = true;
        },
        // 监听 - 页面关闭
        onClose() { },
        // 监听 - 提交成功后
        onFormSuccess() {
            that.value.show = false; // 关闭页面
            that.$emit('success');
        },

        print2() {
            printJS({
                printable: `tableId-0`, // 'printFrom', // 标签元素id
                type: 'html',
                style: '@page {margin:1 1mm;font-size:14px}  .daying {border-spacing:0;width:100%;border-collapse:collapse;font-family:宋体;color:#000} .daying td {border:1px solid #000;height:30px;padding:5px} .daying td label {font-size:14px} .daying td span{font-size:16px}', // 可选-打印时去掉眉页眉尾
                scanStyles: false,
                ignoreElements: [], // ['no-print']
                properties: null
            });
        }
    },
    // 监听属性
    watch: {},
    // 计算属性
    computed: {
        first15SampleList() {
            return this.sample_list.slice(0, 15);
        },
        // 使用计算属性来获取剩余的样本
        remainingSampleList() {
            return this.sample_list.slice(15);
        }
    }
};
</script>

<style lang="scss" scoped>
.page-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
}

.contract-table {
    // font-family: SimSun, serif;
    // max-width: 1000px;
    // margin: 0 auto;
    // border: 1px solid #000;
    padding: 5px;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.logo {
    width: 150px;
}

h1 {
    font-size: 24px;
    font-weight: bold;
}

table {
    width: 100%;
    border-collapse: collapse;
}

td {
    border: 1px solid #000;
    padding: 5px;
    font-size: 14px;
}

.label {
    font-weight: bold;
    background-color: #f0f0f0;
}



.footer {
    margin-top: 20px;
    font-size: 12px;
}
</style>
