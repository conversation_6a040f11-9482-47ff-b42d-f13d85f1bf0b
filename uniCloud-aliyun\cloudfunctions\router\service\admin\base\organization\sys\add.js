module.exports = {
	/**
	 * 添加单条数据
	 * @url admin/kong/sys/add 前端调用的url参数地址
	 * data 请求参数 说明
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: 'ok' };
		// 业务逻辑开始-----------------------------------------------------------
		let {
			sort,
			name,
			code,
			parent_id,
			description,
			is_on
		} = data;
		let dbName = "tm-organization";
		let dataJson = {
			sort,
			name,
			code,
			parent_id,
			description,
			is_on
		};
		// 检测parent_id是否不存在
		if (vk.pubfn.isNotNull(parent_id)) {
			let num = await vk.baseDao.count({
				dbName,
				whereJson: {
					_id: parent_id
				}
			});
			if (num <= 0) {
				return { code: -1, msg: `父标识【${parent_id}】不存在!` };
			}
		} else {
			delete dataJson.parent_id;
		}

		res.id = await vk.baseDao.add({
			dbName,
			dataJson: dataJson
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}
