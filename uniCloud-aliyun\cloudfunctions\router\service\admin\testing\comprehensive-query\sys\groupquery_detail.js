module.exports = {
    /**
     * 查询多条记录 分页
     * @url admin/kong/sys/getList 前端调用的url参数地址
     * data 请求参数 说明
     * @param {Number}         pageIndex 当前页码
     * @param {Number}         pageSize  每页显示数量
     * @param {Array<Object>}  sortRule  排序规则
     * @param {object}         formData  查询条件数据源
     * @param {Array<Object>}  columns   查询条件规则
     * res 返回参数说明
     * @param {Number}         code      错误码，0表示成功
     * @param {String}         msg       详细信息
     */
    main: async (event) => {
        let { data = {}, userInfo, util, filterResponse, originalParam } = event;
        let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
        let { uid } = data;
        let res = { code: 0, msg: '' };
        // 业务逻辑开始-----------------------------------------------------------
        let dbName = "tm-sample-testing";
        let { detection_category } = data.formData;


        let _detection_category = await vk.baseDao.findByWhereJson({
            dbName: "tm-detection_category",
            whereJson: {
                name: detection_category,
            }
        });
        res = await vk.baseDao.getTableData({
            dbName,
            data,
            foreignDB: [
                {
                    dbName: "tm-detection-form",
                    localKey: "detectionform_id",
                    foreignKey: "_id",

                    as: "detection_form",
                    limit: 1,
                },

                {
                    dbName: "tm-client",
                    localKey: "client",
                    foreignKey: "_id",
                    foreignDB: [
                        {
                            dbName: "tm-client-price",
                            localKey: "_id",
                            foreignKey: "client_id",
                            whereJson: {
                                category_id: _detection_category._id
                            },
                            as: "cate_price",
                            limit: 1,
                        },],
                    as: "client_info",
                    limit: 1,
                }
            ]
        });
        return res;
    }

}
