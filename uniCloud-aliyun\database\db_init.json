{"opendb-admin-log": {"data": [{"_id": "65f1e79b337a9fefccc37277", "user_id": "001", "user_name": "超级管理员", "title": "用户添加", "ip": "**************", "url": "admin/system/user/sys/add", "request_param": {"gender": 0, "login_appid_type": 1, "allow_login_background": false, "dcloud_appid": [], "username": "gys001", "nickname": "供应商1"}, "response": {"code": 0, "msg": "", "type": "register", "uid": "65f1e79b466d41f58521d3ce", "userInfo": {"_id": "65f1e79b466d41f58521d3ce", "role": [], "dcloud_appid": [], "register_date": 1710352283426, "username": "gys001", "password": "e12e90ff322e030569586a9b0f2c256cfc1f83d0", "allow_login_background": false, "gender": 0, "nickname": "供应商1", "status": 0}}, "request_id": "ac1cd36e1710352283184187161", "_add_time": 1710352283531, "_add_time_str": "2024-03-14 01:51:23"}]}, "opendb-admin-menus": {"data": [{"_id": "sys-admin", "_add_time": 1596416400000, "comment": "系统内置", "enable": true, "icon": "el-icon-s-tools", "menu_id": "sys-admin", "name": "系统管理", "sort": 100, "url": ""}, {"_id": "sys-role-manage", "_add_time": 1596416400000, "comment": "1个角色可以分配多个权限和菜单", "enable": true, "icon": "el-icon-user", "menu_id": "sys-role-manage", "name": "角色管理", "parent_id": "sys-admin", "sort": 2, "url": "/pages_plugs/system/role/list"}, {"_id": "sys-app-manage", "_add_time": 1596416400000, "comment": "应用管理", "enable": true, "icon": "el-icon-cloudy", "menu_id": "sys-app-manage", "name": "应用管理", "parent_id": "sys-admin", "sort": 5, "url": "/pages_plugs/system/app/list"}, {"_id": "sys-app-upgrade-center", "_add_time": 1596416400000, "comment": "管理和发布新的app版本", "enable": true, "icon": "vk-icon-shengji3-xianxing", "menu_id": "sys-app-upgrade-center", "name": "App升级中心", "parent_id": "sys-admin", "sort": 6, "url": "/pages_plugs/system/app-upgrade-center/list"}, {"_id": "sys-menus-manage", "_add_time": 1596416400000, "comment": "控制admin左侧菜单的显示和隐藏", "enable": true, "icon": "el-icon-tickets", "menu_id": "sys-menus-manage", "name": "菜单管理", "parent_id": "sys-admin", "sort": 4, "url": "/pages_plugs/system/menu/list"}, {"_id": "system-uni-vk-components-dynamic", "_add_time": 1596416400000, "enable": true, "icon": "", "menu_id": "system-uni-vk-components-dynamic", "name": "动态组件数据", "parent_id": "system-uni", "sort": 3, "url": "/pages_plugs/system_uni/vk-components-dynamic"}, {"_id": "sys-user-manage", "_add_time": 1596416400000, "comment": "1个角色可以分配多个角色", "enable": false, "icon": "el-icon-s-custom", "menu_id": "sys-user-manage", "name": "用户管理", "parent_id": "sys-admin", "sort": 1, "url": "/pages_plugs/system/user/list", "hidden_menu": true}, {"_id": "system-uni-uni-id-files", "_add_time": 1596416400000, "enable": true, "icon": "el-icon-folder-opened", "menu_id": "system-uni-uni-id-files", "name": "素材管理", "parent_id": "system-uni", "sort": 1, "url": "/pages_plugs/system_uni/uni-id-files/list"}, {"_id": "system-uni-vk-global-data", "_add_time": 1596416400000, "enable": true, "icon": "", "menu_id": "system-uni-vk-global-data", "name": "系统缓存管理", "parent_id": "system-uni", "sort": 2, "url": "/pages_plugs/system_uni/vk-global-data"}, {"_id": "system-uni-opendb-admin-log", "_add_time": 1596416400000, "enable": true, "icon": "", "menu_id": "system-uni-opendb-admin-log", "name": "系统操作日志", "parent_id": "system-uni", "sort": 9, "url": "/pages_plugs/system_uni/opendb-admin-log"}, {"_id": "sys-permission-manage", "_add_time": 1596416400000, "comment": "1个权限可以匹配多个云函数", "enable": true, "icon": "vk-icon-lock", "menu_id": "sys-permission-manage", "name": "权限管理", "parent_id": "sys-admin", "sort": 3, "url": "/pages_plugs/system/permission/list"}, {"_id": "system-uni", "_add_time": 1596416400000, "enable": true, "icon": "el-icon-s-tools", "menu_id": "system-uni", "name": "设置", "sort": 110}, {"_id": "system-uni-uni-id-log", "_add_time": 1596416400000, "enable": true, "icon": "", "menu_id": "system-uni-uni-id-log", "name": "用户登录日志", "parent_id": "system-uni", "sort": 8, "url": "/pages_plugs/system_uni/uni-id-log"}, {"_id": {"$oid": "65def701e0ec199b18e290c6"}, "menu_id": "jcsjgl", "name": "基础数据管理", "url": "", "icon": "vk-icon-sortlight", "sort": 0, "enable": true, "_add_time": 1709111041186, "_add_time_str": "2024-02-28 17:04:01"}, {"_id": {"$oid": "65def74da09a9b12d7e88d41"}, "menu_id": "jcsjgl-", "name": "检测类型管理", "url": "/pages/detection_category/list", "icon": "vk-icon-circlefill", "sort": 1, "parent_id": "jcsjgl", "enable": true, "_add_time": 1709111117549, "_add_time_str": "2024-02-28 17:05:17"}, {"_id": {"$oid": "65e591e56e5d2ddb51ae6221"}, "menu_id": "jcsjgl-wtdw", "name": "委托单位管理", "url": "/pages/client/list", "sort": 0, "parent_id": "jcsjgl", "enable": true, "_add_time": 1709543909309, "_add_time_str": "2024-03-04 17:18:29"}, {"_id": {"$oid": "65e59275f08210b07de81afe"}, "menu_id": "jcsjgl--sblxgl", "name": "商品类型管理", "url": "/pages/goods_type/list", "sort": 0, "parent_id": "jcsjgl", "enable": true, "_add_time": 1709544053545, "_add_time_str": "2024-03-04 17:20:53"}, {"_id": {"$oid": "65e5929fa09a9b12d7def5ec"}, "menu_id": "jcsjgl-spgggl", "name": "商品规格管理", "url": "/pages/goods_spe/list", "sort": 0, "parent_id": "jcsjgl", "enable": true, "_add_time": 1709544095947, "_add_time_str": "2024-03-04 17:21:35"}, {"_id": {"$oid": "65e592b1189f86e3703f97bb"}, "menu_id": "jcsjgl-spgl", "name": "商品管理", "url": "/pages/goods/list", "sort": 0, "parent_id": "jcsjgl", "enable": true, "_add_time": 1709544113557, "_add_time_str": "2024-03-04 17:21:53"}, {"_id": {"$oid": "65e592bea7c432936bf73800"}, "menu_id": "jcsjgl-gysgl", "name": "供应商管理", "url": "/pages/supplier/list", "sort": 0, "parent_id": "jcsjgl", "enable": true, "_add_time": 1709544126774, "_add_time_str": "2024-03-04 17:22:06"}, {"_id": {"$oid": "65e6f0e655b3372a1f9e4c90"}, "menu_id": "jcsjgl-zzjggl", "name": "组织架构管理", "url": "/pages/organization/list", "icon": "vk-icon-myfill", "sort": 0, "parent_id": "jcsjgl", "enable": true, "_add_time": 1709633766707, "_add_time_str": "2024-03-05 18:16:06"}, {"_id": {"$oid": "65e9765799c6244dcf2b0811"}, "menu_id": "sys-admin-jcgl", "name": "检测管理", "url": "", "sort": 1, "enable": true, "_add_time": 1709798999105, "_add_time_str": "2024-03-07 16:09:59"}, {"_id": {"$oid": "65e976928620667bb4d8d9c2"}, "menu_id": "sys-admin-jcgl-jcd", "name": "检测单", "url": "/pages/detection-form/list", "icon": "vk-icon-goodsnewfill", "sort": 0, "parent_id": "sys-admin-jcgl", "enable": true, "_add_time": 1709799058206, "_add_time_str": "2024-03-07 16:10:58"}, {"_id": {"$oid": "65f1e818189f86e3703a4bb2"}, "menu_id": "jcsjgl-psgsjj", "name": "配送公司简介管理", "url": "/pages/deliveryinfo/info", "icon": "vk-icon-zhankaicaidan", "sort": 0, "parent_id": "jcsjgl", "enable": true, "_add_time": 1710352408319, "_add_time_str": "2024-03-14 01:53:28"}, {"_id": {"$oid": "65f1f92d337a9fefccc4645c"}, "menu_id": "sys-admin-myuser", "name": "用户管理", "url": "/pages/user/list", "icon": "vk-icon-QQ", "sort": 0, "parent_id": "sys-admin", "enable": true, "_add_time": 1710356781449, "_add_time_str": "2024-03-14 03:06:21"}, {"_id": {"$oid": "65f2b3b96e5d2ddb51cdd05b"}, "menu_id": "ddgl", "name": "订单管理", "url": "", "icon": "vk-icon-cangku", "sort": 3, "enable": true, "_add_time": 1710404537581, "_add_time_str": "2024-03-14 16:22:17"}, {"_id": {"$oid": "65f2b3d5337a9fefcce3a6da"}, "menu_id": "ddgl-sydd", "name": "溯源订单", "url": "/pages/origin-detectionfrom/list", "sort": 0, "parent_id": "ddgl", "enable": true, "_add_time": 1710404565185, "_add_time_str": "2024-03-14 16:22:45"}, {"_id": {"$oid": "65f5384399c6244dcf17ee8e"}, "menu_id": "sys-admin-jcgl-", "name": "提前录单", "url": "/pages/detection-form/beforelist", "icon": "vk-icon-form", "sort": 3, "parent_id": "sys-admin-jcgl", "enable": true, "_add_time": 1710569539634, "_add_time_str": "2024-03-16 14:12:19"}, {"_id": {"$oid": "65f53886fe975f7440355541"}, "menu_id": "sys-admin-jcgl-zhcx", "name": "综合查询", "url": "/pages/detection-form/query", "icon": "vk-icon-circle", "sort": 0, "parent_id": "sys-admin-jcgl", "enable": true, "_add_time": 1710569606229, "_add_time_str": "2024-03-16 14:13:26"}]}, "opendb-app-list": {"data": [{"_id": "001", "_add_time": 1596416400000, "_add_time_str": "2020-08-03 09:00:00", "appid": "__UNI__01F080F", "create_date": 1596416400000, "description": "此为用户端应用", "name": "用户端", "type": "client"}]}, "opendb-app-versions": [], "opendb-open-data": [], "opendb-tempdata": [], "opendb-verify-codes": [], "tm-client": {"data": [{"_id": "65ec12be55b3372a1f704b81", "name": "佛山市顺德区品顺餐饮管理服务有限公司", "_add_time": 1709970110302, "_add_time_str": "2024-03-09 15:41:50", "is_productiondate": false}], "index": [{"IndexName": "_name_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "_organization_id_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "organization_id", "Direction": "1"}], "MgoIsUnique": false}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-client-price": {"data": [{"_id": "6607d5096e5d2ddb513daec0", "client_id": "65fc008799c6244dcf35970b", "client_name": "肇庆市航空学校", "category_name": "呋喃它酮代谢物", "category_id": "65f841eff08210b07dec6866", "price": "22", "_X_ROW_KEY": "row_26", "_add_time": 1711789321724, "_add_time_str": "2024-03-30 17:02:01"}], "index": [{"IndexName": "client_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "client_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "client_name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "client_name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "category_name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "category_name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "category_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "category_id", "Direction": "1"}], "MgoIsUnique": false}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-deliveryinfo": {"data": [{"_id": "65f1f2a3e0ec199b18dab09b", "user_id": "001", "enterprise_image": ["https://mp-5fe77170-8511-415d-8f6e-02d647d2979a.cdn.bspapp.com/cloudstorage/21c77a16-e31a-47cd-838a-58ff22e59c62.jpg"], "job_description": "主营业务：我司主营国内贸易，包括但不限于食品、保健食品销售；涉及企业管理服务、餐饮管理服务、餐饮配送服务;做蔬菜、水果、肉类、蛋类、农产品批发道路运输城市服务；提供农产品甄选、集采、仓储、配送一体化的端到端的解决方案；拥有符合国家卫生标准的生产工厂。", "job_image": [], "person2": "https://mp-5fe77170-8511-415d-8f6e-02d647d2979a.cdn.bspapp.com/cloudstorage/4bc1be62-86e1-4c26-9134-fa8ca43f1ddf.jpg", "_add_time": 1710355107015, "_add_time_str": "2024-03-14 02:38:27", "antivirus_records": "", "enterprise_info": "广东甜派商贸有限公司是一家以食材供应链整合服务为核心业务的综合性食堂餐配服务企业，提供肉、菜、禽蛋、水产等农产品以及牛奶、副食品等食材原料甄选、集采、仓储、配送一体化、端到端的解决方案。近年来，我司经过竞标获得桂城街道教育局2018-2021学年公办中学、公办幼儿园、实验类学校食材供应服务资格，多年来，我们慎始如终、长期致力于为桂城街道各学校提供优质的食材配送服务，得到了桂城街道教育局的高度认可，"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-detection-form": {"data": [{"_id": "65fba959337a9fefcc53638a", "no": "24032111282569154", "detection_user_sign": "https://mp-5fe77170-8511-415d-8f6e-02d647d2979a.cdn.bspapp.com/cloudstorage/ad06a295-a9e2-43e9-b26f-3d3b7e424568.png", "_dd": "guangdongquananjc_30", "detection_type": "送检", "detection_category": "65f3b35ff08210b07d2abc69", "detection_standard_name": "农药残留", "detection_standard": "GB/T 5009.199-2003酶抑制法", "client": "65ec12be55b3372a1f704e85", "client_name": "西六美食坊", "detection_user": "李小娟", "submitUser": "广东省南方传媒发行物流有限公司", "detect_time": 1710950400000, "_add_time": 1710991705054, "_add_time_str": "2024-03-21 11:28:25", "printcount": 2}], "index": [{"IndexName": "_no_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "no", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "_client_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "client", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "_name_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "_detection_user_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "detection_user", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "_client_name_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "client_name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "__add_time_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "detection_category", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "detection_category", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "submitUser", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "submitUser", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "remark", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "remark", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "detect_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "detect_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "detection_standard_name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "detection_standard_name", "Direction": "1"}], "MgoIsUnique": false}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-detection_category": {"data": [{"_id": "65e58f408b0da4a4e41e8c84", "name": "酸价", "is_on": true, "_add_time": 1709543232381, "_add_time_str": "2024-03-04 17:07:12", "standard": "快速检测卡法"}], "index": [{"IndexName": "name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": true}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-goods": {"data": [{"_id": "65ec14f58620667bb44bb1cb", "name": "红肉干花生", "_add_time": 1709970677804, "_add_time_str": "2024-03-09 15:51:17", "is_on": true, "detection_category": ["65f84116bd022087df835bf0"], "goods_spe": "65e7081fa7c432936b314578", "goods_type": "65fbadf5fe975f74404059a5", "detection_category_name": ["二氧化硫"], "type_name": "南北干货"}], "index": [{"IndexName": "_name_", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "goods_type", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "goods_type", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "type_name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "type_name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "detection_category", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "detection_category", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "detection_category_name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "detection_category_name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "goods_spe", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "goods_spe", "Direction": "1"}], "MgoIsUnique": false}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-goods_spe": {"data": [{"_id": "65e7081fa7c432936b314578", "name": "1kg", "_add_time": 1709639711794, "_add_time_str": "2024-03-05 19:55:11"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-goods_type": {"data": [{"_id": "65e70cd699c6244dcfcd1caf", "name": "瓜果蔬菜", "_add_time": 1709640918349, "_add_time_str": "2024-03-05 20:15:18", "is_on": true, "is_nocheck": true, "is_shelflife": true}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-organization": {"data": [{"_id": "65e6f31be0ec199b1810f727", "sort": 0, "name": "广东全安安全检测科技有限公司", "_add_time": 1709634331875, "_add_time_str": "2024-03-05 18:25:31"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-sample-testing": {"data": [{"_id": "65fba959a09a9b12d76cee30", "detectionform_id": "65fba959337a9fefcc53638a", "sampleno": "NYCL001", "samplename": "通心菜", "sample_id": "65fa1c868620667bb48a8b4f", "detection_category": "农药残留", "result": "经检测农药残留未超出相关要求", "yxresult": "阴性", "isSave": 1, "_add_time": 1710991705088, "_add_time_str": "2024-03-21 11:28:25", "is_supplier_save": true, "supplier_id": "65f3103f55b3372a1f95d2ab", "supplier_name": "广州苏衡蔬菜配送有限公司（肇庆高要分公司）", "thirdparty_orderno": "33", "unit": "1.0/kg"}], "index": [{"IndexName": "detectionform_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "detectionform_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "sample_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sample_id", "Direction": "1"}], "MgoIsUnique": false}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "tm-supplier": {"data": [{"_id": "65e80c7c8620667bb49dd921", "name": "高要区金渡镇四眼妹蔬菜档", "supplier_type": "65e70cd699c6244dcfcd1caf", "delivery_company": "65ec12be55b3372a1f704e02", "_add_time": 1709706364481, "_add_time_str": "2024-03-06 14:26:04", "delivery_company_name": "佛山市南海区洪厨农副产品配送中心"}], "index": [{"IndexName": "name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": true}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "uni-id-log": {"data": [{"_id": "65d94a4efe975f7440f75730", "type": "login", "login_type": "password", "user_id": "001", "ip": "**************", "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36", "os": "windows", "platform": "h5", "state": 1, "dcloud_appid": "__UNI__7275A8D", "date": {"year": 2024, "month": 2, "day": 24, "hour": 9, "minute": 45, "second": 49, "millisecond": 994, "week": 6, "quarter": 1, "date_str": "2024-02-24 09:45:49", "date_day_str": "2024-02-24", "date_month_str": "2024-02"}, "_add_time": 1708739150034, "_add_time_str": "2024-02-24 09:45:50"}]}, "uni-id-permissions": {"data": [{"_id": "sys-permission", "_add_time": 1596416400000, "comment": "系统内置权限", "enable": true, "permission_id": "sys-permission", "permission_name": "系统内置权限", "sort": 103, "url": ""}]}, "uni-id-roles": {"data": [{"_id": "001", "_add_time": 1596416400000, "comment": "系统内置角色 - 请勿修改", "enable": true, "permission": [], "role_id": "admin", "role_name": "超级管理员"}]}, "uni-id-users": {"data": [{"_id": "001", "allow_login_background": true, "nickname": "超级管理员", "password": "3af3a92a7cb19af1c0a504ccfcf7216ea87703fd", "register_date": 1596416400000, "register_ip": "127.0.0.1", "role": ["admin"], "token": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwMDEiLCJyb2xlIjpbImFkbWluIl0sInBlcm1pc3Npb24iOltdLCJpYXQiOjE3MTY5MzkyMTMsImV4cCI6MTcxNzU0NDAxM30.20Y1OqLvx0c-HDtJPAzx-IEQojKD-ohsecfEp9lHvIk", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwMDEiLCJyb2xlIjpbImFkbWluIl0sInBlcm1pc3Npb24iOltdLCJpYXQiOjE3MTcxNDA3MTgsImV4cCI6MTcxNzc0NTUxOH0.aQ5fPg2p3MNyoNMsQdwugksx_lLQshOjlwd_OgTkuJA", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwMDEiLCJyb2xlIjpbImFkbWluIl0sInBlcm1pc3Npb24iOltdLCJpYXQiOjE3MTczMjgwODAsImV4cCI6MTcxNzkzMjg4MH0.u-s10lY2GTfvSZdoK9FhYgi3ZdTx8PAUFJSvKfkC98k"], "username": "admin", "last_login_date": 1717475445147, "last_login_ip": "**************", "login_ip_limit": []}]}, "vk-components-dynamic": {"index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "data_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "data_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "title", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "title", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "type", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "type", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "sort", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sort", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "show", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "show", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-files": {"data": [{"_id": "65fcd342e0ec199b1896be38", "user_id": "001", "sort": 0, "status": 0, "type": "image", "url": "https://mp-5fe77170-8511-415d-8f6e-02d647d2979a.cdn.bspapp.com/cloudstorage/097aaf25-357c-44d6-8325-27a73b5efde6.png", "display_name": "shr.611fdb4.png", "original_name": "shr.611fdb4.png", "size": 86716, "file_id": "https://mp-5fe77170-8511-415d-8f6e-02d647d2979a.cdn.bspapp.com/cloudstorage/097aaf25-357c-44d6-8325-27a73b5efde6.png", "provider": "unicloud", "width": 806, "height": 338, "_add_time": 1711067970544, "_add_time_str": "2024-03-22 08:39:30"}], "index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "user_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "user_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "sort", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sort", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "status", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "status", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "type", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "type", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "display_name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "display_name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "url", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "url", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-files-categories": {"index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "sort", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sort", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-global-data": {"index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "key", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "key", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "expired_at", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "expired_at", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-pay-config": {"index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-test": {"data": [{"_id": "65ec12547ad52dfccc5caba6", "0": "佛", "1": "山", "2": "市", "3": "顺", "4": "德", "5": "区", "6": "品", "7": "顺", "8": "餐", "9": "饮", "10": "管", "11": "理", "12": "服", "13": "务", "14": "有", "15": "限", "16": "公", "17": "司", "_add_time": 1709970004570, "_add_time_str": "2024-03-09 15:40:04"}], "index": [{"IndexName": "location", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "location", "Direction": "2dsphere"}], "MgoIsUnique": false}}, {"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}]}}