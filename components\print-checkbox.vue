<template>
    <view style="display: flex;justify-content: start;align-items: center;">
        <view v-for="o in options" style="display: flex;justify-content: start;align-items: center;margin-right: 5px;">
            <img v-if="o == value" src="@/static/icon/checked.png" width="18px;" height="18px;" />
            <img v-else src="@/static/icon/check.png" width="18px;" height="18px;" />
            <view style="margin: 0px 8px;">
                {{ o }}
            </view>
        </view>


    </view>
</template>

<script>
export default {
    props: {

        options: {
            Type: Array,
            default: function () {
                return [];
            }
        },
        value: {
            Type: Object,
            default: function () {
                return false;
            }
        }
    },
    data() {
        // 页面数据变量
        return {};
    }
};
</script>

<style scoped></style>
