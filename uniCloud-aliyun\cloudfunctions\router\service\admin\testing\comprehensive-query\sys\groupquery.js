module.exports = {
    /**
     * 查询多条记录 分页
     * @url admin/kong/sys/getList 前端调用的url参数地址
     * data 请求参数 说明
     * @param {Number}         pageIndex 当前页码
     * @param {Number}         pageSize  每页显示数量
     * @param {Array<Object>}  sortRule  排序规则
     * @param {object}         formData  查询条件数据源
     * @param {Array<Object>}  columns   查询条件规则
     * res 返回参数说明
     * @param {Number}         code      错误码，0表示成功
     * @param {String}         msg       详细信息
     */
    main: async (event) => {
        let { data = {}, userInfo, util, filterResponse, originalParam } = event;
        let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
        let { uid } = data;
        var $ = _.aggregate; // 聚合查询操作符
        let res = { code: 0, msg: '' };
        // 业务逻辑开始-----------------------------------------------------------
        let dbName = "tm-sample-testing";
        res = await vk.baseDao.getTableData({
            dbName: dbName,
            pageIndex: 1,
            pageSize: -1,
            data,
            foreignDB: [{
                dbName: "tm-detection-form",
                localKey: "detectionform_id",
                foreignKey: "_id",
                as: "detectionformInfo",
                fieldJson: {
                    client: true,
                    client_name: true,
             
                },
                limit: 1,
            }],
            groupJson: {
                _id: "$detection_category", // _id是分组id（_id:为固定写法，必填属性），这里指按user_id字段进行分组
                sample_id: $.first("$sample_id"),
                samplename: $.first("$detection_category"),
            
                
                // 这里是为了把user_id原样输出
                count: $.sum(1), // count记录条数
            },

        });
        return res;
    }

}
