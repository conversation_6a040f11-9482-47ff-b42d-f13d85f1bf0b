<template>
    <view class="page-body">
        <!-- 页面内容开始 -->

        <!-- 表格搜索组件开始 -->
        <vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" size="mini"
            @search="search"></vk-data-table-query>
        <!-- 表格搜索组件结束 -->

        <!-- 自定义按钮区域开始 -->
        <view>
            <el-row>
                <el-button type="success" size="mini" icon="el-icon-circle-plus-outline" @click="addBtn">添加</el-button>
                <!-- <el-button type="success" size="small" icon="el-icon-circle-plus-outline" @click="addByJsonBtn">通过JSON数组批量导入</el-button> -->

                <el-dropdown v-if="table1.multipleSelection" :split-button="false" trigger="click" @command="batchBtn">
                    <el-button type="danger" size="mini" style="margin-left: 20rpx"
                        :disabled="table1.multipleSelection.length === 0">
                        批量操作
                        <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="1"
                            v-if="$hasPermission(`detection-form-del`)">批量确认收款</el-dropdown-item>
                        <el-dropdown-item :command="2"
                            v-if="$hasPermission(`detection-form-del`)">批量删除</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </el-row>
        </view>

        <!-- 表格组件开始 -->
        <vk-data-table ref="table1" :rowNo="true" :border="true" :action="table1.action" :columns="table1.columns"
            :query-form-param="queryForm1" size="mini" :right-btns="['detail_auto', 'update', 'delete', 'more']"
            :right-btns-more="table1.rightBtnsMore" :selection="true" :pagination="true" @update="updateBtn"
            @delete="deleteBtn" @current-change="currentChange" @selection-change="selectionChange"
            :custom-right-btns="table1.customRightBtns">
                        <template v-slot:originfile="{ row, column, index }">
                <el-button v-if="$fn.isNotNull(row.originfile)" size="small" type="success" circle
                    icon="el-icon-check"></el-button>
            </template>
            <template v-slot:invoicefile="{ row, column, index }">
                <el-button v-if="$fn.isNotNull(row.invoicefile)" size="small" type="success" circle
                    icon="el-icon-check"></el-button>
            </template>
                        <template v-slot:isPay="{ row, column, index }">
                <el-button v-if="$fn.isNotNull(row.isPay)" size="small" type="success" circle
                    icon="el-icon-check"></el-button>
            </template>
        </vk-data-table>
        <!-- 表格组件结束 -->

        <!-- 添加或编辑的弹窗开始 -->
        <vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="1300px" mode="form"
            :close-on-click-modal="false">
            <vk-data-form v-model="form1.data" ref="form1" :rules="form1.props.rules" :action="form1.props.action"
                :form-type="form1.props.formType" inline :columns="form1.props.columns" :columns-number="2" size="mini"
                label-width="180px" @success="
                    form1.props.show = false;
                refresh();
                ">
                <template v-slot:sample_list="{ form, keyName }">
                    <el-button type="success" size="mini" icon="el-icon-circle-plus-outline" style="margin: 10px 5px"
                        @click="insertEvent()">添加样品</el-button>

                    <view style="width: 100%; overflow-x: scroll">
                        <vxe-table ref="xTable" style="width: 1250px" keep-source border resizable show-overflow
                            size="mini" :data="form[keyName]">
                            <vxe-table-column fixed="left" type="seq" width="80"></vxe-table-column>
                            <vxe-table-column field="sampleno" title="样品编号" align="left" width="120"></vxe-table-column>
                            <vxe-table-column field="samplename" width="130" title="样品名称"
                                align="left"></vxe-table-column>
                            <vxe-table-column field="specs" min-width="100" title="数量/规格" align="left"
                                width="90"></vxe-table-column>
                            <vxe-table-column field="price" title="费用(元)" align="left" width="80"></vxe-table-column>
                            <vxe-table-column field="trademark" min-width="80" title="商标" align="left"
                                width="80`"></vxe-table-column>
                            <vxe-table-column field="qualityGrade" min-width="100" title="质量等级/类型" align="left"
                                width="150"></vxe-table-column>
                            <vxe-table-column field="lotNumber" min-width="100" title="生产日期/批号" align="left"
                                width="150"></vxe-table-column>
                            <vxe-table-column field="origin" min-width="100" title="产地" align="left"
                                width="150"></vxe-table-column>
                            <vxe-table-column field="productUnits" min-width="100" title="生产单位" align="left"
                                width="150"></vxe-table-column>
                            <vxe-table-column field="detection_categorynames" width="150" title="检测项目和方法"
                                align="left"></vxe-table-column>

                            <vxe-table-column field="criteria" min-width="100" title="判定标准" align="left"
                                width="150"></vxe-table-column>
                            <vxe-table-column field="zizhizang" min-width="100" title="资质章" align="left"
                                width="150"></vxe-table-column>
                            <vxe-column title="操作" width="150" fixed="right">
                                <template #default="{ row }">
                                    <template>
                                        <vxe-button @click="editEvent(row)">编辑</vxe-button>
                                        <vxe-button status="danger" @click="removeEvent(row)">删除</vxe-button>
                                    </template>
                                </template>
                            </vxe-column>
                        </vxe-table>
                    </view>
                </template>
            </vk-data-form>
        </vk-data-dialog>
        <!-- 添加或编辑的弹窗结束 -->
        <vk-data-dialog v-model="form2.props.show" :title="form2.props.title" width="700px" mode="form"
            :close-on-click-modal="false">
            <vk-data-form v-model="form2.data" :rules="form2.props.rules" :action="form2.props.action"
                :form-type="form2.props.formType" :columns="form2.props.columns" label-width="120px"
                :before-action="form2.props.beforeAction" @success="
                    form2.props.show = false;
                refresh();
                "></vk-data-form>

            >
        </vk-data-dialog>
        <vk-data-dialog v-model="form3.props.show" :title="form3.props.title" width="700px" mode="form"
            :close-on-click-modal="false">
            <vk-data-form v-model="form3.data" :rules="form3.props.rules" :action="form3.props.action"
                :form-type="form3.props.formType" :columns="form3.props.columns" label-width="120px"
                :before-action="form3.props.beforeAction" @success="
                    form3.props.show = false;
                refresh();
                "></vk-data-form>

            >
        </vk-data-dialog>
        <vk-data-dialog append-to-body top="10vh" mode="form" v-model="showSampleEdit"
            :title="selectRow ? '编辑&保存' : '新增&保存'" destroy-on-close>
            <vxe-form :data="formData" :rules="formRules" @submit="marksureEditrSmaple" title-align="right"
                title-width="130">
                <vxe-form-item title="" title-align="left" :title-width="200" :span="24"
                    :title-prefix="{ icon: 'vxe-icon-comment' }"></vxe-form-item>
                <vxe-form-item field="sampleno" title="样品编号" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.sampleno" placeholder="请输入名称"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="sample_category" title="样品类型" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vk-data-input-select size="small" v-model="data.sample_category" :filterable="true"
                            :localdata="goods_types" placeholder="请选择" :props="{ value: '_id', label: 'name' }"
                            width="150px"
                            @change="(val, formData, column, index, option) => { }"></vk-data-input-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="samplename" title="样品名称" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vk-data-input-select size="small" v-model="data.sample_id" :filterable="true"
                            :localdata="goodsfilters" placeholder="请选择" :props="{ value: '_id', label: 'name' }"
                            width="200px" @change="(val, formData, column, index, option) => {
                                handelSampleChane(option, formData, data);
                            }
                                "></vk-data-input-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="specs" title="数量/规格" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.specs" placeholder="请输入数量/规格"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="trademark" title="商标" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.trademark" placeholder="请输入商标"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="qualityGrade" title="质量等级/类型" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.qualityGrade" placeholder="质量等级/类型"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="lotNumber" title="生产日期/批号" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.lotNumber" placeholder="生产日期/批号"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="origin" title="产地" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.origin" placeholder="产地"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="productUnits" title="生产单位" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.productUnits" placeholder="生产单位"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="detection_category" title="检测项目" :span="24" :item-render="{}">
                    <template #default="{ data }">
                        <vk-data-input-select size="small" multiple v-model="data.detection_category" :filterable="true"
                            :localdata="detection_categorys" placeholder="请选择" :props="{ value: '_id', label: 'name' }"
                            width="500px" @change="(val, formData, column, index, option) => {

                                handeldetection_categoryChange(val, formData, data);
                            }"></vk-data-input-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="detection_standards" title="检测方法" :span="24" :item-render="{}">
                    <template #default="{ data }">
                        <vk-data-input-select size="small" multiple v-model="data.detection_standards"
                            :filterable="true" :localdata="detection_standard_list" placeholder="请选择"
                            :props="{ value: '_id', label: 'name' }" width="500px"></vk-data-input-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="criteria" title="判定标准" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.criteria" placeholder="判定标准"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="zizhizang" title="资质章" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.zizhizang" placeholder="资质章"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item field="price" title="价格" :span="12" :item-render="{}">
                    <template #default="{ data }">
                        <vxe-input v-model="data.price" disabled placeholder="计算得出无法修改"></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item align="center" title-align="left" :span="24">
                    <template #default>
                        <vxe-button size="small" style="margin: 10px 5px" type="submit">确定</vxe-button>
                        <!-- <vxe-button @click="marksureEditrSmaple">确定</vxe-button> -->
                        <!-- <vxe-button type="reset">重置</vxe-button> -->
                    </template>
                </vxe-form-item>
            </vxe-form>
        </vk-data-dialog>
        <print v-model="print" />
        <!-- 页面内容结束 -->
    </view>
</template>

<script>
import { remove, concat, find, sumBy, unionBy, groupBy, map } from 'lodash';
import print from './print'
var that; // 当前页面对象
var vk = uni.vk; // vk实例
var originalForms = {}; // 表单初始化数据

export default {
    components: {
        print
    },
    data() {
        // 页面数据变量
        return {
            // 页面是否请求中或加载中
            loading: false,
            showSampleEdit: false,
            selectRow: false,
            print: {
                show: false,
                printData: []
            },
            organizations: [],
            //检测标准列表
            detection_standard_list: [],
            detection_categorys: [],
            goods: [],
            goods_types: [],
            // init请求返回的数据
            data: {},
            formData: {},
            // 表格相关开始 -----------------------------------------------------------
            table1: {
                // 表格数据请求地址
                action: 'admin/contract/contract/sys/getList',
                // 表格字段显示规则
                columns: [
                    // { key:"_id", title:"id", type:"text", width:220 },
                    
  { key: 'contractNumber', title: '合同编号', type: 'text', width: 100, sortable: 'custom' },
                    { key: 'name', title: '合同名称', type: 'text', width: 150, sortable: 'custom' },
                    { key: 'client_name', title: '合同委托人/单位', type: 'text', width: 255, sortable: 'custom' },

 { key: 'originfile', title: '盖章合同', type: 'text', sortable: 'custom' },
                    {
                        key: 'invoicefile', title: '已上传发票', type: 'html', width: 100, formatter: function (val, row, column, index) {
                            if (vk.pubfn.isNotNull(val)) {
                                return '<el-button type="primary" icon="el-icon-check"></el-button>'
                            }
                        }, sortable: 'custom'
                    },
                   
                    { key: 'isPay', title: '收款状态', type: 'text', width: 100, activeValue: true, inactiveValue: false, sortable: 'custom' },
                    // { key: 'sort', title: '排序', type: 'number', width: 100, sortable: 'custom' }
  { key: 'contact_name', title: '联系人', type: 'text', width: 100, sortable: 'custom' },
                    { key: '_add_time', title: '添加时间', type: 'time', width: 130, sortable: 'custom' }
                    // { key:"_add_time", title:"距离现在", type:"dateDiff", width:120 },
                ],
                // 多选框选中的值
                multipleSelection: [],
                // 当前高亮的记录
                selectItem: '',
                customRightBtns: [
                    {
                        title: '打印',
                        type: 'success',
                        icon: 'el-icon-plus',
                        onClick: item => {
                            that.print.printData = [];
                            that.print.show = true;

                            that.print.printData.push(vk.pubfn.deepClone(item));
                        }
                    },

                ],
                rightBtnsMore: [
                    {
                        title: '上传原件',
                        show: item => {
                            return this.$hasRole('admin') || this.$hasPermission('detection-form-edittime');
                        },
                        onClick: item => {
                            that.form2.props.action = 'admin/contract/contract/sys/upload_originfile';
                            that.form2.props.formType = 'update';
                            that.form2.props.title = '合同原件上传';
                            that.form2.props.show = true;
                            that.form2.data = item;
                        }
                    },
                    {
                        title: '上传发票',
                        show: item => {
                            return this.$hasRole('admin') || this.$hasPermission('detection-form-edittime');
                        },
                        onClick: item => {
                            that.form3.props.action = 'admin/contract/contract/sys/upload_invoicefile';
                            that.form3.props.formType = 'update';
                            that.form3.props.title = '发票原件上传';
                            that.form3.props.show = true;
                            that.form3.data = item;
                        }
                    },

                ]
            },
            // 表格相关结束 -----------------------------------------------------------
            // 表单相关开始 -----------------------------------------------------------
            // 查询表单请求数据
            queryForm1: {
                // 查询表单数据源，可在此设置默认值
                formData: {},
                // 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
                columns: [{ key: 'name', title: '配送公司名称', type: 'text', width: 160, mode: '%%' }]
            },
            form1: {
                // 表单请求数据，此处可以设置默认值
                data: {},
                // 表单属性
                props: {
                    // 表单请求地址
                    action: '',
                    // 表单字段显示规则
                    columns: [
                        { key: 'name', title: '合同名称', type: 'text', width: 400, sortable: 'custom', oneLine: true },
                        {
                            key: 'client',
                            title: '委托单位/人',
                            type: 'remote-select',
                            placeholder: '请选择委托单位',
                            filterable: true,
                            action: 'admin/base/client/sys/getalllist',
                            props: { list: 'rows', value: '_id', label: 'name' },

                            showAll: true,
                            actionData: {
                                pageSize: -1
                            },
                            watch: ({ value, formData, column, index, option, $set }) => {
                                $set('client_name', option.name);
                            },
                            width: 300
                        },

                        { key: 'contact_name', title: '联系人', type: 'text', sortable: 'custom', width: 400 },
                        { key: 'mobile', title: '联系电话', type: 'text', sortable: 'custom', width: 400 },
                        { key: 'address', title: '地址', type: 'text', sortable: 'custom', width: 400 },

                        {
                            key: 'check_range',
                            title: '检测周期',
                            type: 'radio',
                            data: [
                                { value: '标准5-8天', label: '标准5-8天' },
                                { value: '加急3-5天', label: '加急3-5天' },
                                { value: '特急1-2天', label: '特急1-2天' }
                            ],

                            sortable: 'custom'
                        },
                        {
                            key: 'isHasImg',
                            title: ' 报告体现样品照片',
                            type: 'radio',
                            data: [
                                { value: '是', label: '是' },
                                { value: '否', label: '否' }],

                            sortable: 'custom'
                        },

                        {
                            key: 'report_send_type',
                            title: '报告送达方式',
                            type: 'radio',
                            data: [
                                { value: '自取', label: '自取' },
                                { value: '邮寄', label: '邮寄' },
                                { value: '传真', label: '传真' }
                            ],

                            sortable: 'custom'
                        },
                        {
                            key: 'pay_type',
                            title: ' 付款支付方式',
                            type: 'radio',
                            data: [
                                { value: '转账', label: '转账' },
                                { value: '刷卡', label: '刷卡' }
                            ],

                            sortable: 'custom'
                        },

                        {
                            key: 'sampleHandling',
                            title: '样品处理',
                            type: 'radio',
                            data: [
                                { value: '客户取回', label: '客户取回' },
                                { value: '由本实验室销毁', label: '由本实验室销毁' }
                            ],

                            sortable: 'custom'
                        },
                        {
                            key: 'IsreportDetermined',
                            title: '检测报告是否判定',
                            type: 'radio',
                            data: [
                                { value: '是', label: '是' },
                                { value: '否', label: '否' }],

                            sortable: 'custom'
                        },

                        {
                            key: 'check_type',
                            title: '检测类型',
                            type: 'radio',
                            data: [
                                { value: '委托检验', label: '委托检验' },
                                { value: '监督抽检', label: '监督抽检' }
                            ],

                            sortable: 'custom'
                        },
                        {
                            key: 'isSub',
                            title: ' 是否同意检测分包',
                            type: 'radio',
                            data: [
                                { value: '是', label: '是' },
                                { value: '否', label: '否' },],

                            sortable: 'custom'
                        },
                        {
                            key: 'detectionFrom',
                            title: '检测方法',

                            type: 'radio',
                            data: [
                                { value: '由委托方指定', label: '由委托方指定' },
                                { value: '由检测方决定', label: '由检测方决定' },
                                { value: '由双方协商决定', label: '由双方协商决定' }
                            ],

                            sortable: 'custom'
                        },

                        {
                            key: 'isAgreementCustomers',
                            title: '是否协议客户',
                            type: 'radio',
                            data: [
                                { value: '协议客户', label: '是' },
                                { value: '否', label: '否' }],

                            sortable: 'custom'
                        },
                        {
                            key: 'contractNumber',
                            title: '合同编号',
                            type: 'text',

                            sortable: 'custom'
                        },

                        {
                            key: 'inspections',
                            title: '常规检测计费',
                            type: 'radio',
                            data: [
                                { value: '是', label: '是' },
                                { value: '否', label: '否' }],

                            sortable: 'custom'
                        },
                        {
                            key: 'invoicing',
                            title: '开票方式',
                            type: 'radio',
                            data: [
                                { value: '普通发票', label: '普通发票' },
                                { value: '专用发票', label: '专用发票' }
                            ],
                            oneLine: true,
                            sortable: 'custom'
                        },
                        {
                            key: 'discount',
                            title: '折扣',
                            type: 'percentage',

                            sortable: 'custom'
                        },
                        {
                            key: 'totalSettlement',
                            title: '结算费用合计',
                            type: 'text',
                            disabled: true,
                            sortable: 'custom'
                        },

                        { key: 'originfile', title: '合同原件', type: 'image', limit: 3, disabled: true, oneLine: true },

                        { key: 'invoicefile', title: '发票原件', type: 'image', limit: 3, disabled: true, oneLine: true },
                        { key: 'sample_list', title: '', type: 'text', width: 1200, oneLine: true }
                    ],
                    // 表单验证规则
                    rules: {
                        name: [
                            // 必填
                            { required: true, message: "合同名称不能为空", trigger: 'change' }
                        ],
                        client: [
                            // 必填
                            { required: true, message: "委托单位/人不能为空", trigger: 'change' }
                        ],
                        contact_name: [
                            // 必填
                            { required: true, message: "联系人不能为空", trigger: 'change' }
                        ],
                        mobile: [
                            // 必填
                            { required: true, message: "联系人电话不能为空", trigger: 'change' }
                        ],
                        contractNumber: [
                            // 必填
                            { required: true, message: "合同编号不能为空", trigger: 'change' }
                        ],
                        totalSettlement: [
                            // 必填
                            { required: true, message: "结算费用必须先生成", trigger: 'change' }
                        ],
                    },
                    // add 代表添加 update 代表修改
                    formType: '',
                    // 是否显示表单的弹窗
                    show: false
                }
            },
            form2: {
                // 表单请求数据，此处可以设置默认值
                data: {},
                // 表单属性
                props: {
                    // 表单请求地址
                    action: '',
                    // 表单字段显示规则
                    columns: [
                        {
                            key: 'originfile',
                            title: '合同原件上传',
                            type: "image",

                        }
                    ],
                    // 表单验证规则
                    rules: {},
                    beforeAction(formData) {

                    },
                    // add 代表添加 update 代表修改
                    formType: 'update',
                    // 是否显示表单的弹窗
                    show: false
                }
            },
            form3: {
                // 表单请求数据，此处可以设置默认值
                data: {},
                // 表单属性
                props: {
                    // 表单请求地址
                    action: '',
                    // 表单字段显示规则
                    columns: [
                        {
                            key: 'invoicefile',
                            title: '发票原件上传',
                            type: "image",

                        }
                    ],
                    // 表单验证规则
                    rules: {},
                    beforeAction(formData) {

                    },
                    // add 代表添加 update 代表修改
                    formType: 'update',
                    // 是否显示表单的弹窗
                    show: false
                }
            },
            // 其他弹窗表单
            formDatas: {},
            formRules: {
                sampleno: [
                    { required: true, message: '请输入样品编号' },
                    { min: 3, max: 15, message: '长度在 3 到 15 个字符' }
                ],
                sample_category: [
                    { required: true, message: '请输入样品类型' }
                ],
                samplename: [
                    { required: true, message: '请输入样品名称' }
                ],
                detection_category: [
                    { required: true, message: '检测项目' }
                ],
                specs: [
                    { required: true, message: '请输入数量/规格' }
                ],
            }
            // 表单相关结束 -----------------------------------------------------------
        };
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        that.init(options);
    },
    onUnload() {
        // 返回false阻止页面被销毁
        return false;
    },

    // 函数
    methods: {
        // 页面数据初始化函数
        init(options) {
            originalForms['form1'] = vk.pubfn.copyObject(that.form1);
            vk.callFunction({
                url: 'admin/base/organization/sys/getflat',
                data: {
                    page: 1,
                    pageSize: -1
                },
                success: data => {
                    this.organizations = data.rows;
                }
            });
            vk.callFunction({
                url: 'admin/base/goods_type/sys/getList',
                data: {
                    page: 1,
                    pageSize: -1
                },
                success: data => {
                    this.goods_types = data.rows;
                }
            });
            vk.callFunction({
                url: 'admin/base/detection_category/sys/getList',
                data: {
                    page: 1,
                    pageSize: -1
                },
                success: data => {
                    this.detection_categorys = data.rows;
                    this.detection_standard_list = map(groupBy(data.rows, 'standard'), (group, key) => ({ name: key, _id: key }));
                }
            });
            vk.callFunction({
                url: 'admin/base/goods/sys/getAllList',
                success: data => {
                    this.goods = data.rows;
                }
            });
        },
        organizationfomattr(val, row, column, index) {
            // console.log(this.categories.find(x=>x._id==val))
            return this.organizations.find(x => x._id == val).name;
        },
        userInfoFomattr(val, row, column, index) {
            if (this.$fn.isNotNull(val)) return val.map(x => x.nickname).join(',');
            else return '';
        },
        // 页面跳转
        pageTo(path) {
            vk.navigateTo(path);
        },
        // 表单重置
        resetForm() {
            vk.pubfn.resetForm(originalForms, that);
        },
        // 搜索
        search() {
            that.$refs.table1.search();
        },
        // 刷新
        refresh() {
            that.$refs.table1.refresh();
        },
        // 获取当前选中的行的数据
        getCurrentRow() {
            return that.$refs.table1.getCurrentRow();
        },
        // 监听 - 行的选中高亮事件
        currentChange(val) {
            that.table1.selectItem = val;
        },
        // 当选择项发生变化时会触发该事件
        selectionChange(list) {
            that.table1.multipleSelection = list;
        },
        // 显示添加页面
        addBtn() {
            that.resetForm();
            that.form1.props.action = 'admin/contract/contract/sys/add';
            that.form1.props.formType = 'add';
            that.form1.props.title = '添加';
            that.form1.props.show = true;
            let _detection_categorys;
        },
        // 显示修改页面
        async updateBtn({ item }) {
            that.form1.props.action = 'admin/contract/contract/sys/update';
            that.form1.props.formType = 'update';
            that.form1.props.title = '编辑';
            that.form1.props.show = true;
            that.form1.data = item;
        },
        // 删除按钮
        deleteBtn({ item, deleteFn }) {
            deleteFn({
                action: 'admin/contract/contract/sys/delete',
                data: {
                    _id: item._id
                }
            });
        },
        // 监听 - 批量操作的按钮点击事件
        batchBtn(index) {
            switch (index) {
                case 1:
                    vk.toast('批量操作按钮1');
                    break;
                case 2:
                    this.mutifiDel();
                    break;
                default:
                    break;
            }
        },
        handelDetection_categoryChane(val, form, row) { },
        insertEvent() {
            this.selectRow = null;
            this.showSampleEdit = true;
            this.formData = {
                detection_include: '',
                trademark: '',
                qualityGrade: '',
                lotNumber: '',
                origin: '',
                productUnits: '',
                detection_category: '',
                criteria: '',
                zizhizang: ''
            };
        },
        editEvent(row) {
            this.formData = JSON.parse(JSON.stringify(row));
            this.selectRow = row;
            this.showSampleEdit = true;
        },
        async marksureEditrSmaple() {
            this.showSampleEdit = false;
            if (this.$fn.isNotNull(this.selectRow)) {
                this.selectRow = JSON.parse(JSON.stringify(this.formData));

                await vk.callFunction({
                    url: 'admin/contract/contract/sys/getPrice',
                    data: {
                        client_id: this.formData.client_id,
                        category_ids: this.formData.detection_category
                    },
                    success: data => {
                        that.$set(this.formData, 'price', data.result);
                        // data.result;
                    }
                });
                let index = this.form1.data.sample_list.findIndex(x => x._X_ROW_KEY == this.formData._X_ROW_KEY);
                if (index !== -1) {
                    that.$set(this.form1.data.sample_list, index, JSON.parse(JSON.stringify(this.formData)));
                }
                this.$refs.xTable.loadData(this.form1.data.sample_list);
                //   console.log(this.form1.data.sample_list);
                this.formData = '';
            } else {
                if (this.$fn.isNull(this.form1.data.sample_list)) {
                    this.form1.data.sample_list = [];
                }
                await vk.callFunction({
                    url: 'admin/contract/contract/sys/getPrice',
                    data: {
                        client_id: this.formData.client_id,
                        category_ids: this.formData.detection_category
                    },
                    success: data => {
                        that.$set(this.formData, 'price', data.result);
                        // data.result;
                    }
                });
                this.form1.data.sample_list.push(JSON.parse(JSON.stringify(this.formData)));
                this.$refs.xTable.loadData(this.form1.data.sample_list);
            }
        },
        mutifiDel() {
            let _delids = that.table1.multipleSelection.map(x => x._id);
            vk.callFunction({
                url: 'admin/testing/detection-form/sys/batchdel',
                title: '请求中...',
                data: {
                    ids: _delids
                },
                success: data => { }
            });
        },
        handelSampleChane(val, formData, row) {
            // console.log(val, formData, row);
            this.$set(row, 'samplename', formData.name);
            console.log(formData, 'change');
            // this.$set(row, 'sample_id', formData.id);
        },
        handeldetection_categoryChange(val, formData, row) {
            // console.log(val, formData, row);
            //   this.$set(row, 'samplename', formData.name);
            console.log(formData, 'change');
            console.log(row, 'change');
            let _name = formData.map(x => x.name).join(',')
            this.$set(row, 'detection_categorynames', _name);
            // this.$set(row, 'sample_id', formData.id);
        },
    },
    // 监听属性
    watch: {
        'form1.data.sample_list': {
            handler() {
                // 当 list 发生变化时，重新计算所有行的 area
                that.$nextTick(() => {
                    console.log(that.form1.data.discount);
                    if (that.$fn.isNotNull(that.form1.data.discount)) {
                        that.$set(that.form1.data, 'totalSettlement', sumBy(that.form1.data.sample_list, x => x.price) * that.form1.data.discount);
                    } else {
                        that.$set(that.form1.data, 'totalSettlement', sumBy(that.form1.data.sample_list, x => x.price));
                    }
                });
            },
            deep: true
        },
        'form1.data.discount': {
            handler() {
                // 当 list 发生变化时，重新计算所有行的 area
                that.$nextTick(() => {
                    if (that.$fn.isNotNull(that.form1.data.discount)) {
                        that.$set(that.form1.data, 'totalSettlement', sumBy(that.form1.data.sample_list, x => x.price) * that.form1.data.discount);
                    } else {
                        that.$set(that.form1.data, 'totalSettlement', sumBy(that.form1.data.sample_list, x => x.price));
                    }
                })
            }
        }
    },
    // 过滤器
    filters: {},
    // 计算属性
    computed: {
        detection_categorysfilters: function () { },
        goodsfilters: function () {
            if (this.$fn.isNotNull(this.formData.sample_category)) return this.goods.filter(x => x.goods_type == this.formData.sample_category);
            else return this.goods;
            //return this.goods.filter(x => includes(x.detection_category, this.form1.data.detection_category))
        }
    }
};
</script>

<style lang="scss" scoped>
.page-body {}

/v-deep/.el-input.is-disabled .el-input__inner {
    color: #1b2231;
}
</style>
