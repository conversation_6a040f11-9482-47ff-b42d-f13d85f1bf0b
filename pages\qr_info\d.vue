<template>
    <view class="page" style="overflow-x: scroll">
        <div style="width: 100%; align-content: center">
            <div style="z-index: -1; display: flex; -ms-flex-align: center; align-items: center; -ms-flex-pack: center;  -ms-flex-direction: column; flex-direction: column" >
                <div>
                    <div style="display: flex; width: 100%; font-family: 宋体; margin-top: 20px">
                        <div style="width: 185px">
                            <img src="@/static/print-log.png" height="80px" width="170px" />
                        </div>
                        <div style="display: flex; flex-direction: column; justify-content: center">
                            <div>
                                <span style="font-size: 30px; color: black; display: ruby-text; font-weight: bold; line-height: 34px; flex-wrap: nowrap; width: 100%">深圳市金阅检测科技有限责任公司</span>
                            </div>
                            <div style="color: black; font-size: 23px; font-weight: bold; display: ruby-text">Shenzhen King Eye Testing Technology Co., Ltd</div>
                        </div>
                    </div>
                </div>
                <div style="width: 95%; padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px"></div>
                <div style="width: 95%; padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px"></div>
                <div style="position: relative; width: 95%; font-family: 宋体">
                    <div style="font-size: 30px !important; font-weight: bold; color: black; margin-top: 10px; text-align: center">检 测 报 告</div>
                </div>
                <div style="width: 95%">
                    <div style="margin-bottom: 5px; display: flex; justify-content: space-between">
                        <div style="display: flex; justify-content: start">
                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">报告编号：</span>
                            <span style="font-size: 16px; font-family: 宋体; color: black">{{ formdata.no }}</span>
                        </div>

                    </div>
                    <div>
                                <table class="daying" style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr>
                                        <td colspan="4" align="center" style="height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">客户信息及检测信息</span></td>
                                        <td rowspan="6" class="qrcode-cell" style="border: 1px solid #000; width: 20%">
                                            <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%">
                                                <vue-qr :logoSrc="logourl" :margin="2" :text="`https://static-host-uebpmzce-web.gzg.sealos.run/#/pages/qr_info/d?no=${ formdata.no}`" :size="120"></vue-qr>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">委托单位名称</span></td>
                                        <td align="center" colspan="3" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{  formdata.client_name }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">委托单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{  formdata.address || '广东省佛山市三水区耀华路8号' }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受 检 单 位</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{  formdata.submitUser }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受检单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{  formdata.submituser_address || formdata.addressps||'' }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">接 收 日 期</span></td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat( formdata.detect_time, 'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样 品 状 态</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">鲜样正常</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">验 证 日 期</span></td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat( formdata.detect_time, 'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检 测 类 型</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">委托检测</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 12px">扫描二维码辨别报告真伪</span>
                                        </td>
                                    </tr>
                                </table>
                    </div>
                </div>
                <div style="align-items: center; font-size: 18px; margin-top: 6px; margin-bottom: 6px; font-family: 宋体; color: black">检 测 结 果</div>
                <div style="width: 95%">
                    <div>
                        <table class="daying" style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                            <tr style="height: 30px">
                                <td align="center" width="5%" style="border: 1px solid #000; height: 15px"><span style="font-weight: bold; font-size: 16px; font-family: 黑体">序号</span></td>
                                <td align="center" width="15%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品编号</span></td>
                                <td align="center" width="15%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品名称</span></td>
                                <td align="center" colspan="2" width="17%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测项目</span></td>
                                <td align="center" width="15%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测方法</span></td>
                                <td align="center" width="13%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测限</span></td>
                                <td align="center" width="10%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">测试结果</span></td>
                                <td align="center" width="15%" style="border: 1px solid #000; height: 15px">
                                    <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">{{ getremarkTitle(formdata.sample_list) }}</span>
                                </td>
                            </tr>

                            <!-- 动态行高和字体大小 -->
                            <tr v-for="(detail, index) in formdata.sample_list" :key="index" :style="{ height: calcRowHeight(formdata.sample_list.length) + 'px', fontSize: calcFontSize(formdata.sample_list.length) + 'px' }">
                                <td align="center" style="border: 1px solid #000; height: 15px">{{ index + 1 }}</td>
                                <td align="center" style="border: 1px solid #000; height: 15px">{{ detail.sampleno }}</td>
                                <td align="center" style="border: 1px solid #000; height: 15px">{{ detail.samplename }}</td>
                                <td align="center" colspan="2" style="border: 1px solid #000; height: 15px">{{ detail.detection_category }}</td>
                                <td align="center" style="border: 1px solid #000; height: 15px">{{ detail.detection_standard }}</td>
                                <td align="center" style="border: 1px solid #000; height: 15px">{{ detail.detection_include }}</td>
                                <td align="center" style="border: 1px solid #000; height: 15px" :style="{ 'font-size': '14px' }">
                                    {{ detail.result }}
                                </td>
                                <td align="center" style="border: 1px solid #000; height: 15px" :style="{ 'font-size': '13px' }">
                                    {{ detail.remark }}
                                </td>
                            </tr>
                        </table>


                    </div>
                                   <div style="text-align: left; padding: 5px 5px;font-family: 黑体;  color: #606266 ;padding-left: 10px; font-size: 12px;border: 1px solid #000; border-top: none;">1、报告无快检专用章无效；报告无检测人、审核人签名无效，报告经涂改、增删无效；</br>
2、以上样品信息均由客户提供，本司不承担其信息准确性的责任；</br>
3. 本报告只对送检样品检测结果负责。以上检测结果为快速检测定性检测结果，不具备法律效力，仅供客户参考。</br>
4、委托单位对本检测报告有异议，请在收到报告之日或指定领取报告之日起，三个工作日内提出申诉，逾期不子受理。</div>
                        </div>
                                 <!--?印章-->
                <div style="background-color: aquamarine; width: 95%; font-size: 16px; font-weight: bold; margin-bottom: 10%; position: relative" :style="{ 'margin-top': formdata.sample_list.length > pageSzie ? '-2px' : '2px' }">
                    <div style="z-index: 1; position: absolute; margin-left: 100%; background-color: transparent" :style="{ transform: formdata.sample_list.length > pageSzie ? ' translateX(-140%) translateY(-20%)' : ' translateX(-140%) translateY(-20%)' }">
                        <img src="@/static/gz.png" width="200px;" height="150px;" />
                    </div>
                    <div style="margin-left: 100%; z-index: 2; white-space: nowrap; position: absolute; transform: translateX(-100%); font-family: 黑体">此处未盖本公司快检专用章，则本报告无效。</div>
                </div>
                <!--?印章-->
                <div style="display: flex; flex-direction: row; justify-content: space-between; width: 95%; z-index: 1">
                    <div style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                        <div>检测人:</div>
                        <div style="position: absolute; left: 28%; top: -80%">
                            <span>
                                <img v-if="$fn.isNotNull(formdata.detection_user_info.sign_image)" width="100px" height="40px" :src="formdata.detection_user_info.sign_image" />
                            </span>
                        </div>
                    </div>
                    <div style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                        <div>审核人:</div>
                        <div style="position: absolute; left: 28%; top: -60%">
                            <img src="@/static/shr.png" width="100px" height="40px" />
                        </div>
                    </div>
                    <div style="width: 34%; font-size: 16px; font-weight: bold; font-family: 黑体">
                        <span>签发日期:</span>
                        <span>{{ vk.pubfn.timeFormat(formdata.detect_time, 'yyyy年MM月dd日') }}</span>
                    </div>
                </div>
                
                <div style="margin-top: auto; width: 95%; padding-bottom: 20px">
                    <div style="margin-top: 20px; margin-bottom: 20px; text-align: center; font-size: 18px">***报告完结***</div>
                    <div style="padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px"></div>
                    <div style="padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px"></div>
                    <div style="display: flex; align-content: space-between; color: #606266; margin-top: 10px">
                        <div style="font-size: 12px; font-family: 黑体; display: ruby-text"> 0755-28380866 深圳市金阅检测科技有限责任公司</div>
                        <div style="margin-left: 15%; font-size: 12px; font-family: 黑体; align-content: flex-end; color: #606266">地址：深圳市大鹏新区大鹏街道布新社区布新村工业大道2号C401</div>
                    </div>
              
                </div>
                </div>
        
       

            </div>
     
    </view>
</template>
<script>
import VueQr from 'vue-qr';
var that; // 当前页面对象
var vk = uni.vk; // vk实例
export default {
    components: {
        VueQr
    },
    data() {
        // 页面数据变量
        return {
            activeName: 'first',
            formdata: {},
            logourl: require('@/static/logo.png'),
            count: 0,
            pageSzie: 22,
            pageSzie2: 56,
            pageSzie3: 90,
            pageSzie4: 155,
            onePageHeight: 1200
        };
    },
    // 监听 - 页面每次【加载时】执行(如：前进)
    onLoad(options = {}) {
        that = this;
        vk = that.vk;
        that.options = options;
        that.init(options);
        // console.log(vk.getVuex('$user.userInfo').nickname)
        // this.form1.data.enterprise_name = vk.getVuex('$user.userInfo').nickname
    },
    // 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
    onReady() {},
    // 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
    onShow() {},
    // 监听 - 页面每次【隐藏时】执行(如：返回)
    onHide() {},
    // 函数
    methods: {
        // 页面数据初始化函数
        init(options) {
            vk.callFunction({
                url: 'client/pub.get_detectionfrom',
                title: '请求中...',
                data: {
                    no: options.no
                },
                success: data => {
                    that.formdata = data.data;
                    that.count = that.formdata.sample_list.length;

                    if (this.formdata.sample_list.length < 10) {
                        const row = 10 - this.formdata.sample_list.length;
                        for (let i = 0; i < row; i++) {
                            this.formdata.sample_list.push({
                                sampleno: i == 0 ? '以下空白' : ''
                            });
                        }
                    } else if (this.formdata.sample_list.length > 10 && this.formdata.sample_list.length < 20) {
                        const row = 20 - this.formdata.sample_list.length;
                        for (let i = 0; i < row; i++) {
                            this.formdata.sample_list.push({
                                sampleno: i == 0 ? '以下空白' : ''
                            });
                        }
                    } else if (this.formdata.sample_list.length > 10 && this.formdata.sample_list.length < 30) {
                        const row = 30 - this.formdata.sample_list.length;
                        for (let i = 0; i < row; i++) {
                            this.formdata.sample_list.push({
                                sampleno: i == 0 ? '以下空白' : ''
                            });
                        }
                    } else if (this.formdata.sample_list.length > 10 && this.formdata.sample_list.length < 40) {
                        const row = 40 - this.formdata.sample_list.length;
                        for (let i = 0; i < row; i++) {
                            this.formdata.sample_list.push({
                                sampleno: i == 0 ? '以下空白' : ''
                            });
                        }
                    }
                }
            });
        },
        getremarkTitle(list) {
            if (vk.pubfn.isNotNull(list) && list.length > 0) {
                if (list[0].detection_category == '农药残留' || list[0].detection_category == '有机磷') return '抑制率';
            }
            return '备注';
        },
        getremark(form) {
            if (vk.pubfn.isNotNull(form.sample_list) && form.sample_list.length > 0 && vk.pubfn.isNotNull(form.remark)) {
                if (form.sample_list[0].detection_category == '农药残留' || form.sample_list[0].detection_category == '有机磷') return form.remark;
            }
            return '';
        },
        getsubList(list, isfirst) {
            if (isfirst) {
                return list.slice(0, this.pageSzie);
            } else {
                return list.slice(this.pageSzie);
            }
        },

        calcRowHeight(length) {
            // return length < 20 ? 30 : 20;
            return 30;
        },
        calcFontSize(length) {
            return 14;
            //  return length < 20 ? 14 : 10;
        },
        getSampleSatus(data) {
            let _result = '鲜样正常';

            if (vk.pubfn.isNotNull(data.sample_list)) {
                data.sample_list.forEach(element => {
                    if (vk.pubfn.isNotNull(element.yxresult)) {
                        if (element.yxresult != '阴性') {
                            _result = element.yxresult;
                            return _result;
                        }
                    }
                });
            }
            return _result;
        }
    },
    // 过滤器
    filters: {},
    // 计算属性
    computed: {
        PageHeight() {
            let _height = 1200;
            if (vk.pubfn.isNotNull(this.formdata.sample_list)) {
                if (this.formdata.sample_list.length < 20) {
                    return _height;
                } else {
                    _height = 1200 + 1200 * Math.ceil((this.formdata.sample_list.length - 20) / (this.pageSzie * 2));
                }
            }

            return _height;
        },
        PageCount() {
            let count = 1;
            if (vk.pubfn.isNotNull(this.formdata.sample_list)) {
                if (this.formdata.sample_list.length < 20) {
                    count = 1;
                } else {
                    count = 1 + Math.ceil((this.formdata.sample_list.length - 20) / (this.pageSzie * 2));
                }
            }
            return count;
        }
    }
};
</script>
<style lang="scss" scoped>
body {
    overflow-x: scroll !important;
}

.page {
    padding-bottom: 20px;
}

.daying {
    font-size: 12px;
}

.daying tr td {
    font-size: 14px;
    word-break: break-all;

    border: 1px solid rgb(0, 0, 0);
}
</style>
