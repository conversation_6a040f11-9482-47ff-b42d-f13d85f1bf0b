<template>
    <view class="content">

        <view v-if="!autographStatus" class="px-20" style="padding-bottom: 350rpx;">
            <rich-text :nodes="contract"></rich-text>
            <view class="flex-between" style="font-size: 22rpx;">
                <view class="flex-col">
                    <view class="flex-center" style="height: 100rpx;">甲方：广宁县宏广建设投资有限公司</view>
                    <view>
                        <p>日期：{{ $fn.timeFormat(new Date(), "yyyy年MM月dd日") }} </p>
                    </view>
                </view>
                <view class="flex-col">
                    <view class="flex-start" style="height: 100rpx;">
                        <view>乙方（签名）：</view>
                        <tm-images :previmage="false" v-if="$fn.isNotNull(signpng)" :width="150" :height="225"
                            model="widthFix" :src="signpng"></tm-images>
                    </view>
                    <view>
                        <p>日期：{{ $fn.timeFormat(new Date(), "yyyy年MM月dd日") }} </p>
                    </view>
                </view>

            </view>
        </view>
        <view class="authentication_bottom" v-if="NavAutograph" style="background:#ffe9e9" @click="agreeSign">
            <button style="color:#f00">我已阅读上述内容，同意签字>></button>
        </view>

        <view class="authentication_fun" v-if="Navpreservation" style="background:#fff; ">
            <!-- <button style="color: #1789ff"  @click="preservationImg">保存到本地</button>
			<button style="color: #1789ff"  @click="ReSign">重新签署</button> -->
            <button style="color:#fd972e;" @click="SigningCompleted">签署完成，去提交>></button>
        </view>

        <!-- 签字弹窗 status -->
        <view class="signMask" v-if="autographStatus">
            <view class="sigh-btns">
                <button class="btn" @tap="handleCancel">取消</button>
                <button class="btn" @tap="handleReset">重写</button>
                <button class="btn" @tap="handleConfirm">确认</button>
            </view>
            <view class="sign-box">
                <canvas class="mycanvas" :style="{ width: width + 'px', height: height + 'px' }" canvas-id="mycanvas"
                    @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"></canvas>
                <canvas canvas-id="camCacnvs" :style="{ width: height + 'px', height: width + 'px' }"
                    class="canvsborder"></canvas>
            </view>
        </view>

        <!-- 签字弹窗 end -->

    </view>
</template>
<script>
var x = 20;
var y = 20;
var tempPoint = []; //用来存放当前画纸上的轨迹点
var id = 0;
var type = '';
let that;
let canvasw;
let canvash;
export default {
    data() {
        return {
            signpng: '',
            contract: '',
            info: {
                order_id: '',
                order_no: '',
                realname: vk.getVuex('$user.userInfo.realname'),
                idcard: vk.getVuex('$user.userInfo.idcard'),
                booth_name: '',
                booth_no: '',
                mobile: vk.getVuex('$user.userInfo.mobile'),
                address: vk.getVuex('$user.userInfo.address'),
            },
            ctx: '', //绘图图像
            points: [], //路径点集合,
            width: 0,
            height: 0,
            autographStatus: false,
            publish: false,
            PreviewContract: true,
            NavAutograph: true,
            Navpreservation: false
        }
    },
    onLoad(option) {
        that = this;
        const eventChannel = this.getOpenerEventChannel();
        // 监听data事件，获取上一页面通过eventChannel.emit传送到当前页面的数据
        if (eventChannel.on) {
            eventChannel.on('data', (data) => {
                console.log(data.boothinfo.name);
                that.$set(that.info, 'booth_name', data.boothinfo.name)
                that.$set(that.info, 'booth_no', data.boothinfo.booth_no)
                that.$set(that.info, 'order_id', data.order_id)
                that.$set(that.info, 'order_no', data.order_no)
                this.contract = `<p style=\"text-align: center;\"><span style=\"font-family: Pacifico; font-size: 30rpx;font-weight:600\">2024年广宁县城区迎春展销活动展位租赁协议</span></p><p>\xa0\xa0\xa0\xa0<span style=\"font-size: 13px;\"><p style=\"font-size: 12px;\">甲方（出租人）：广宁县宏广建设投资有限公司</p><p style=\"font-size: 12px;white-space: nowrap;\">乙方（承租人）：<font style="text-decoration: underline;font-weight:600;">\xa0\xa0${this.info.realname}\xa0\xa0</font>身份证号码：<font style="text-decoration: underline;font-weight:600;">\xa0${this.info.idcard}\xa0</font></p><p style=\"font-size: 12px;\">联系电话：<font style="text-decoration: underline;font-weight:600;">\xa0\xa0${this.info.mobile}\xa0\xa0</font> 地址：<font style="text-decoration: underline;font-weight:600;">\xa0\xa0${this.info.address}\xa0\xa0</font></p><p>\xa0\xa0\xa0\xa0<span style=\"font-size: 12px;\">根据《中华人民共和国民法典》及相关的法律、法规，为规范2024年广宁县城区迎春展销活动的展位租赁等事宜，本着平等互利原则，经友好协商一致，达成如下条款共同遵守。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa0</span><strong style=\"font-size: 12px;\">\xa0\xa0\xa0\xa0第一条 租赁场地、期限及用途</strong></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa01、本次展销活动的春联展场在城东广场举办，花卉展场分别在锦绣鸿都、明珠天韵小区周边道路共两个展场举办。</span></p>` +
                    `<p style=\"font-size: 12px;display:content;word-break: normal;\">\xa0\xa0\xa0\xa02、乙方通过指定竞价系统（价高者得）方式获得2024年广宁县迎春展销活动展场<span style="text-decoration: underline;font-size: 12px;font-weight:600;">\xa0\xa0${this.info.booth_name}\xa0\xa0</span>展位编号为<span style="text-decoration: underline;font-size: 12px;font-weight:600;">\xa0\xa0${this.info.booth_no}\xa0\xa0</span>，实际展位面积、四至界限及外观、结构、内在质量以竞拍成交后移交承租人时的现状为准。</p>` +
                    `<p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa03、租赁期限。春联展位租期为15天（即2024年1月26日至2024年2月9日上午12：00），花卉展位租期为9天（即2024年2月1日至2024年2月9日晚上21：00）。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa04、场地用途/经营范围。春联展场主要展销春联、灯笼、门画及年货等应节喜庆用品；花卉展场主要展销鲜花、盆花、盆桔、绿枝、工艺精品等喜庆商品。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa0</span><strong style=\"font-size: 12px;\">第二条&nbsp;协议价款支付方式</strong></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa01、本协议按先付后租原则，租金价款以乙方竞拍出价为准，租金仅为租赁价款，而不含展位物资保管服务费用。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa02、乙方签订本协议前向甲方支付押金&nbsp;&nbsp;1000&nbsp;元/卡，本协议届满后，经甲方确认承租人不存在违约或被扣罚的行为，甲方将于2024年2月29日前将押金无息退还乙方。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa0第三条 场地交付</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa01、甲方应在展销活动正式营业前将租赁展位交付乙方使用。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa02、租赁期满时乙方须清走展位内所有废弃物品，恢复租赁场地原貌，如乙方不按时撤出展场或遗留废弃垃圾等情况，甲方有权没收履约保证金，并取消乙方下年度展销活动展位的投标/承租资格。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa0</span><strong style=\"font-size: 12px;\">第四条&nbsp;经营要求</strong></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa01、乙方必须严格按甲方要求和规定的范围进行经营，不超范围经营、不占道经营，一经发现，甲方有权责令乙方即时整改，限期内不按要求完成整改，甲方有权没收履约保证金作为违约金，并取消乙方下年度展销活动展位的投标/承租资格。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa02、展位严格按功能分区，乙方需在甲方划定的位置内经营，销售的产品必须为合格产品，符合我国相关行业产品的法律法规，不得擅自扩大经营范围，不得擅自增加与展区经营品种功能不同类型商品，否则将视为违约，第一次发现时甲方有权责令乙方立即整改，拒绝整改或不按要求整改的，甲方有权直接没收履约保证金作为违约金；再次发现，甲方有权单方终止本协议，责令乙方立即离场，并没收乙方已交纳的租金和履约保证金。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa03、每卡展位使用照明设备总电功率不得超过60瓦，如有发现违规安装超出用电功率的照明设备、私设插座等行为，拒不整改的，甲方有权单方终止本协议，责令乙方立即离场，并没收乙方已交纳的租金和履约保证金。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa04、花市场内禁止使用明火及大功率电器，一经发现，予以没收。展场内禁止销售或存放易燃易爆物品，例如烟花、爆竹、仿真枪、氢气球和使用明火的灯笼、孔明灯等，因违反管理要求生产的责任由乙方负责。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa05、乙方在租赁期内必须做好安全防火工作并配合甲方做好环境卫生保洁、垃圾清理等工作，必须服从甲方工作人员的现场指挥、自觉维护秩序、爱护公共设施，因乙方行为损坏展场设备设施的，由此产生的一切经济及法律责任由乙方负责。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa06、在租赁期内，乙方销售预包装商品（年货及食品等）的，须在签订本租赁协议时提交相应的食品经营许可证及营业执照等相关证件，销售期内必须提供食品进货凭证及相关产品检验合格证等资料。不准销售散装、自制等食品。因乙方所销售产品的质量问题所引起的一切经济及法律责任均由乙方承担，与甲方无任何关系，且甲方无义务协助乙方解决前述纠纷。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa07、租赁期限内，甲方只确认乙方是该租赁场地唯一的承租人及使用人，未经甲方同意，乙方不得擅自将该租赁展位以转租、转让等方式转租给第三人使用或经营，一经发现即视为乙方违约，甲方有权单方面解除本协议，且扣除乙方已交的租金和履约保证金，责令乙方立即离场，并取消乙方下年度展销活动展位的投标/承租资格，乙方对此无异议。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa08、车辆管理。花市营业期间乙方需遵守甲方的车辆管理规定和现场工作人员的指挥，严禁所有机动车辆进入经营区内。统一补货时间为每晚22:00时至次日9:00时。乙方自用三轮车、货车等需自行按交通法规停放。如乙方违反相关规定乱摆放车辆，甲方有权没收乙方所交纳的履约保证金。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa09、租赁期内，甲方有权利对2024年广宁县迎春展销活动实施统一的管理，管理范围包括但不限于场地、设备、设施、环境卫生、治安消防、经营秩序及各项经营活动等事务的全权管理，乙方需积极配合甲方的管理与指挥，主动积极维护展区的正常运行。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa010、甲方委托工作人员对摆卖区进行日夜巡查和管理，但对乙方的物品没有安保义务，由乙方自行妥善保管所属物品。非甲方原因造成乙方物品损坏、遗失的，甲方不承担任何责任。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa011、展场禁宵时间为每晚10：00-次日8：00。乙方必须在晚上11点前离开展区，由安保人员拉起警界线，若在禁宵时间内进入展区，必先向工作人员出示身份证核对无误并经工作人员同意后方可由工作人员陪同进入展位。</span></p><p><span style=\"font-size: 12px;font-weight:600;\">\xa0\xa0\xa0\xa012、乙方必须在展销活动结束时自行拆除搬走由乙方带来的物品（包括但不限于架枝、架板、板台、未售出的盆景等）；如乙方不按约定与要求清走蓄意就地破坏、损坏或遗弃行为生产垃圾，甲方将没收乙方已交的租金和履约保证金，责令乙方立即离场，取消乙方下年度展销活动展位的投标/承租资格，不足部分由乙方补足，乙方对此无异议。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa013、乙方保证严格执行国家政策、法规，服从行政职能部门管理，合法经营，按规定交纳相关费用。对于违法经营、侵害消费者权益等扰乱正常市场经营秩序的行为，一切后果由乙方承担，<strong>甲方有权单方面解除本协议，没收乙方已交的租金和履约保证金，责令乙方立即离场，取消乙方下年度展销活动展位的投标/承租资格，乙方对此无异议。</strong></span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa0</span><strong style=\"font-size: 12px;\">第五条&nbsp;其他约定&nbsp;</strong></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa01、甲方已依法向乙方提示上述条款内容，乙方已知悉且完全理解上述条款并愿意承担法律后果。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa02、《关于2024年广宁县城区迎春展销活动招商通告》、《关于2024年广宁县城区迎春展销活动展位竞价规则》为本协议不可分割的组成部分，与本协议具有同等法律效力。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa03、乙方保证在本次租赁行为中所提供的信息（包括线上竞拍提供的个人信息）是属于本人的真实完整、合法有效的信息，知悉并完全理解该等信息存在被提供和使用的风险。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa04、在本协议存续期间未尽事宜或发生争议的，经甲、乙双方共同协商作出的补充约定与本协议具有同等效力，协商不成，可依法向广宁县人民法院起诉。</span></p><p><span style=\"font-size: 12px;\">\xa0\xa0\xa0\xa05、本协议书一式2份，甲乙双方各执1份，具有同等法律效力。</span></p><p>\xa0\xa0\xa0\xa0&nbsp;</p>`
                //that.$set(that.info, 'booth_no', '??')
            });
        }
        id = option.id;
        type = option.type;
        this.ctx = uni.createCanvasContext('mycanvas', this); //创建绘图对象
        //设置画笔样式
        this.ctx.lineWidth = 4;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        uni.getSystemInfo({
            success: function (res) {
                that.width = res.windowWidth * 0.75;
                that.height = res.windowHeight * 0.82;
            }
        });
    },
    onShow() {
        const t = this;

        // uni.getStorage({
        // 	key: 'autograph_key',
        // 	success: function (res) {
        // 		if (res.data !== '') {
        // 			t.contractImg = res.data
        // 			t.PreviewContract = true;
        // 			t.publish = false;
        // 			t.NavAutograph = false
        // 		}
        // 	}
        // });
    },

    methods: {
        agreeSign() {
            this.autographStatus = true;
        },

        //触摸开始，获取到起点
        touchstart: function (e) {
            let startX = e.changedTouches[0].x;
            let startY = e.changedTouches[0].y;
            let startPoint = {
                X: startX,
                Y: startY
            };
            /* **************************************************
            #由于uni对canvas的实现有所不同，这里需要把起点存起来
         * **************************************************/
            this.points.push(startPoint);

            //每次触摸开始，开启新的路径
            this.ctx.beginPath();
        },
        //触摸移动，获取到路径点
        touchmove: function (e) {
            let moveX = e.changedTouches[0].x;
            let moveY = e.changedTouches[0].y;
            let movePoint = {
                X: moveX,
                Y: moveY
            };
            this.points.push(movePoint); //存点
            let len = this.points.length;
            if (len >= 2) {
                this.draw(); //绘制路径
            }
            tempPoint.push(movePoint);
        },
        // 触摸结束，将未绘制的点清空防止对后续路径产生干扰
        touchend: function () {
            this.points = [];
        },
        /* ***********************************************	
                #   绘制笔迹
                #   1.为保证笔迹实时显示，必须在移动的同时绘制笔迹
                #   2.为保证笔迹连续，每次从路径集合中区两个点作为起点（moveTo）和终点(lineTo)
                #   3.将上一次的终点作为下一次绘制的起点（即清除第一个点）
                ************************************************ */
        draw: function () {
            let point1 = this.points[0];
            let point2 = this.points[1];
            this.points.shift();
            this.ctx.moveTo(point1.X, point1.Y);
            this.ctx.lineTo(point2.X, point2.Y);
            this.ctx.stroke();
            this.ctx.draw(true);
        },
        handleCancel() {
            uni.navigateBack({
                delta: 1
            });
        },
        //清空画布
        handleReset: function () {
            console.log('handleReset');
            that.ctx.clearRect(0, 0, that.width, that.height);
            that.ctx.draw(true);
            tempPoint = [];
        },
        //将签名笔迹上传到服务器，并将返回来的地址存到本地
        handleConfirm: function () {
            const t = this;
            if (tempPoint.length == 0) {
                uni.showToast({
                    title: '请先签名',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            uni.showLoading({
                title: '生成中'
            });
            uni.canvasToTempFilePath({
                canvasId: 'mycanvas',
                success: function (res) {
                    let tempPath = res.tempFilePath;
                    const ctx = uni.createCanvasContext('camCacnvs', that);
                    ctx.translate(0, that.width);
                    ctx.rotate((-90 * Math.PI) / 180);
                    ctx.drawImage(tempPath, 0, 0, that.width, that.height);
                    ctx.draw();
                    setTimeout(() => {
                        //保存签名图片到本地
                        uni.canvasToTempFilePath({
                            canvasId: 'camCacnvs',
                            success: function (res) {
                                //这是签名图片文件的本地临时地址
                                let path = res.tempFilePath;
                                console.log(res, "保存签名图片到本地")
                                t.signpng = path;
                                t.autographStatus = false
                                // 开始合成
                                var _this = this;
                                t.publish = true;
                                t.PreviewContract = false;
                                t.NavAutograph = false;
                                t.Navpreservation = true;
                                uni.hideLoading();
                                // 合成完毕
                            },
                            fail: err => {
                                // console.log('fail', err);
                            }
                        },
                            this
                        );
                    }, 200);
                }
            });
        },
        preservationImg() {
            const t = this;
            uni.downloadFile({
                url: t.contractImg,
                success: res => {
                    if (res.statusCode === 200) {
                        uni.saveImageToPhotosAlbum({
                            filePath: res.tempFilePath,
                            success: function () {
                                uni.showToast({
                                    title: '保存成功',
                                    icon: 'none',
                                    duration: 2000
                                });
                            },
                            fail: function () {
                                uni.showToast({
                                    title: '保存失败',
                                    icon: 'none',
                                    duration: 2000
                                });
                            }
                        });
                    } else {
                        uni.showToast({
                            title: '第三方网络错误',
                            icon: 'none',
                            duration: 2000
                        });
                    }
                }
            });
        },
        SigningCompleted() {
            const t = this;
            let ImgType = t.signpng.split(".")

            vk.uploadFile({
                title: "上传中...",
                filePath: t.signpng,
                provider: "unicloud", // 指定上传至unicloud空间内置存储（provider可不传，默认会从中配置中读取）
                success: (res) => {
                    vk.callFunction({
                        url: 'client/booths/kh/sign_contract',
                        title: '请求中...',
                        data: {
                            sign_url: res.url,
                            ...this.info,
                            contract: this.contract
                        },
                        success: (data) => {
                            vk.toast("合同签署成功", "success");
                            vk.navigateBack();
                        }
                    });
                },
                fail: (err) => {
                    vk.alert(err)
                }
            });
            return;
        },
        ReSign: function () {
            // console.log(this.ctx)
            this.autographStatus = true
        }
    }
}
</script>
<style>
.authentication_top {
    width: 100%;
    height: 90%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    margin: auto;
    overflow-y: scroll;
    padding-bottom: 40rpx
}

.authentication_top image {
    width: 100%;
    display: inline-block
}

.authentication_bottom {
    width: 100%;
    height: 10%;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #ffe9e9
}

.authentication_bottom button {
    background: 0 0;
    border: none;
    font-size: 15px;
    color: red;
    width: 100%;
    text-align: center
}

.authentication_bottom button:after {
    display: none
}

.authentication_fun {
    width: 100%;
    height: 10%;
    position: fixed;
    bottom: 30rpx;
    left: 0;
    right: 0;
    margin: auto;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background: #ffe9e9;
    padding: 0 3%;
    width: 90%;
    border-radius: 50px;
    box-shadow: 0 3px 13px rgba(0, 0, 0, .2);
    border: 4px solid #fd972e;
    box-sizing: border-box
}

.authentication_fun button {
    border: none;
    font-size: 15px;
    background: 0 0;
    width: 100%;
    text-align: center
}

.authentication_fun button:after {
    display: none
}

.signMask {
    width: 100%;
    height: 100%;
    background: #fff;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    flex-direction: row
}

.sign-box,
.signMask {
    margin: auto;
    display: flex
}

.sign-box {
    width: 80%;
    height: 90%;
    flex-direction: column;
    text-align: center
}

.sigh-btns,
.sign-view {
    height: 100%
}

.sigh-btns {
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-around
}

.btn {
    margin: auto;
    padding: 8rpx 40rpx;
    font-size: 14px;
    transform: rotate(90deg);
    border: 1rpx solid grey
}

.mycanvas {
    margin: auto 0rpx;
    background-color: #ececec
}

.canvsborder {
    border: 1rpx solid #333;
    position: fixed;
    top: 0;
    left: 10000rpx
}

.bgCoverBox {
    width: 100%;
    height: auto
}

.canvsborder2 {
    height: 700px !important
}
</style>
