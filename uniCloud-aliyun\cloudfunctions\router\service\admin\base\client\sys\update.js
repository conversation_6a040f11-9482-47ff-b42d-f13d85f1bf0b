module.exports = {
	/**
	 * 修改数据
	 * @url admin/kong/sys/update 前端调用的url参数地址
	 * data 请求参数 说明
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: 'ok' };
		// 业务逻辑开始-----------------------------------------------------------
		let {
			_id,
			sort,
			name,
			organization_id,
			thumb,
			price_list,
			address,addressps,
			is_productiondate
		} = data;
		// 这里需要把 params1 params2 params3 改成你数据库里允许用户添加的字段
		if (vk.pubfn.isNullOne(_id)) {
			return { code: -1, msg: '参数错误' };
		}
		let dbName = "tm-client";
		await vk.baseDao.updateById({
			dbName,
			id: _id,
			dataJson: {
				sort,
				name,
				organization_id,
				thumb,
				address,addressps,
				is_productiondate
			}
		});

		await vk.baseDao.del({
			dbName: "tm-client-price",
			whereJson: {
				client_id: _id
			}
		});


		let _add_list = price_list.map(x => {
			if (vk.pubfn.isNull(x.client_id)) {
				return {

					'client_id': _id,
					'client_name': name,
					...x
				}
			} else
				return x;

		})
		console.log(_add_list)
		if (vk.pubfn.isNotNull(_add_list)) {
			await vk.baseDao.adds({
				dbName: "tm-client-price",// 表名
				dataJson: _add_list
			});
		}


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}

}
