<template>
    <vk-data-dialog v-model="value.show" :title="page.title" :top="page.top" :width="page.width" mode="form" max-height="800px" width="860px" @open="onOpen" @closed="onClose" :close-on-click-modal="true">
        <div id="tableId-0" ref="printContent">
  
            <div v-for="(v, i) in value.printData" :key="i" :style="i < value.printData.length - 1 ? 'page-break-after: always' : ''">
          
     
                <div style="width: 850px; align-content: center">
    
                    <div style="z-index: -1; display: flex; -ms-flex-align: center; align-items: center; -ms-flex-pack: center;  -ms-flex-direction: column; flex-direction: column" :style="{ height: getPageHeight(v) + 'px' }">
                        <div>
                            <div style="display: flex; width: 100%; font-family: 宋体; margin-top: 20px">
                                <div style="width: 185px">
                                    <img src="@/static/print-log.png" height="80px" width="170px" />
                                </div>
                                <div style="display: flex; flex-direction: column; justify-content: center">
                                    <div>
                                        <span style="font-size: 30px; color: black; display: ruby-text; font-weight: bold; line-height: 34px; flex-wrap: nowrap; width: 100%">深圳市金阅检测科技有限责任公司</span>
                                    </div>
                                    <div style="color: black; font-size: 23px; font-weight: bold; display: ruby-text">Shenzhen King Eye Testing Technology Co., Ltd</div>
                                </div>
                            </div>
                        </div>
                        <div style="width: 95%; padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px"></div>
                        <div style="width: 95%; padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px"></div>
                        <div style="position: relative; width: 95%; font-family: 宋体">
                            <div style="font-size: 30px !important; font-weight: bold; font-weight: bold; color: black; margin-top: 10px; text-align: center">检 测 报 告</div>
                        </div>
                        <div style="width: 95%">
                            <div style="margin-bottom: 5px; display: flex; justify-content: space-between">
                                <div style="display: flex; justify-content: start">
                                    <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">报告编号：</span>
                                    <span style="font-size: 16px; font-family: 宋体; color: black">{{ v.no }}</span>
                                </div>

                                <div style="width: 120px">
                                    <span style="display: ruby-text">
                                        {{ `第${1}页 / 共${getPageCount(v)}页` }}
                                    </span>
                                </div>
                            </div>
                               <table class="daying" style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr>
                                        <td colspan="4" align="center" style="height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">客户信息及检测信息</span></td>
                                        <td rowspan="6" class="qrcode-cell" style="border: 1px solid #000; width: 20%">
                                            <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%">
                                                <vue-qr :logoSrc="logourl" :margin="2" :text="`https://static-host-uebpmzce-web.gzg.sealos.run/#/pages/qr_info/d?no=${v.no}`" :size="120"></vue-qr>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">委托单位名称</span></td>
                                        <td align="center" colspan="3" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.client_name }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">委托单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.address  }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受 检 单 位</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.submitUser }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="height: 30px; border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">受检单位地址</span>
                                        </td>
                                        <td align="center" colspan="3" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ (v.submituser_address || v.addressps) ? (v.submituser_address || v.addressps) : (v.client_name === v.submitUser ? v.address : '') }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体; border: 1pxsolid#000; height: 15px">接 收 日 期</span></td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat(v.detect_time, 'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样 品 状 态</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">鲜样正常</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="width: 15%; height: 30px; border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">验 证 日 期</span></td>
                                        <td align="center" style="width: 35%; border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ vk.pubfn.timeFormat(v.detect_time, 'yyyy年MM月dd日') }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检 测 类 型</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 16px">{{ v.detection_type || '委托检测' }}</span>
                                        </td>
                                        <td align="center" style="border: 1px solid #000; height: 15px">
                                            <span style="font-size: 12px">扫描二维码辨别报告真伪</span>
                                        </td>
                                    </tr>
                                </table>
                                <div>
                                   
                            </div> 
                        </div>
                        <div style="align-items: center; font-size: 18px; font-weight: bold; font-weight: bold; margin-top: 6px; margin-bottom: 6px; font-family: 宋体; color: black">检 测 结 果</div>
                        <div style="width: 95%">
                            <div>
                                <table class="daying" style="border-spacing: 0; width: 100%; border-collapse: collapse; font-family: 宋体; color: #000">
                                    <tr style="height: 30px">
                                        <td align="center" width="4%" style="border: 1px solid #000; height: 15px"><span style="font-weight: bold; font-size: 16px; font-family: 黑体">序号</span></td>
                                        <td align="center" width="11%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品编号</span></td>
                                        <td align="center" width="11%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">样品名称</span></td>
                                        <td align="center" colspan="2" width="16%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测项目</span></td>
                                        <td align="center" width="14%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测方法</span></td>
                                        <td align="center" width="11%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">检测限</span></td>
                                        <td align="center" width="8%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">测试结果</span></td>
                                    <td align="center" width="9%" style="border: 1px solid #000; height: 15px"><span style="color: black; font-weight: bold; font-size: 16px; font-family: 黑体">{{getremarkTitle(v.sample_list)}}</span></td>
                                    </tr>

                                    <!-- 动态行高和字体大小 -->
                                    <tr v-for="(detail, index) in getsubList(v.sample_list, 1)" :key="index" :style="{ height: calcRowHeight(v.sample_list.length) + 'px', fontSize: calcFontSize(v.sample_list.length) + 'px' }">
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ index + 1 }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.sampleno }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.samplename }}</td>
                                        <td align="center" colspan="2" style="border: 1px solid #000; height: 13px">{{ detail.detection_category }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.detection_standard }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px">{{ detail.detection_include }}</td>
                                        <td align="center" style="border: 1px solid #000; height: 13px" :style="{ 'font-size': '14px' }">
                                            {{ detail.result }}
                                        </td>
                                     <td align="center" style="border: 1px solid #000; height: 13px" :style="{ 'font-size': '13px' }">
                                            {{ detail.remark }}
                                        </td>
                                    </tr>
                                </table>

                                <div v-if="v.sample_list.length > pageSzie" style="width: 100%; font-size: 16px; font-weight: bold; margin-top: 3%; margin-bottom: 10%; position: relative">
                                    <div style="z-index: 999; position: absolute; transform: translateX(-140%) translateY(-200px); margin-left: 100%; background-color: transparent">
                                        <img src="@/static/gz.png" width="150px;" height="150px;" />
                                    </div>
                                </div>
                                <print_footer v-if="v.sample_list.length > pageSzie" :pageIndex="`第一页`"></print_footer>
                                <print_content v-if="v.sample_list.length > pageSzie" :pageSzie="pageSzie" :data="getsubList(v.sample_list, 2)" :isLast="v.sample_list.length > pageSzie && v.sample_list.length < pageSzie2" :pageIndex="`第二页`" />
                                <print_content v-if="v.sample_list.length > pageSzie2" :pageSzie="pageSzie2" :data="getsubList(v.sample_list, 3)" :isLast="v.sample_list.length > pageSzie2 && v.sample_list.length < pageSzie3" :pageIndex="`第三页`" />
                                <print_content v-if="v.sample_list.length > pageSzie3" :pageSzie="pageSzie3" :data="getsubList(v.sample_list, 4)" :isLast="v.sample_list.length > pageSzie3 && v.sample_list.length < pageSzie4" :pageIndex="`第四页`" />
                                <print_content v-if="v.sample_list.length > pageSzie4" :pageSzie="pageSzie4" :data="getsubList(v.sample_list, 5)" :isLast="v.sample_list.length > pageSzie4 && v.sample_list.length < pageSzie5" :pageIndex="`第五页`" />
                            </div>
                  <div style="text-align: left; padding: 5px 5px;font-family: 黑体;;color: #000;padding-left: 10px; font-size: 13px;border: 1px solid #000; border-top: none;">注：请客户仔细阅读检测报告的申明</br>1、报告无快检专用章无效；报告无检测人、审核人签名无效，报告经涂改、增删无效；</br>
                    2、以上样品信息均由客户提供，本司不承担其信息准确性的责任；</br>
                    3. 本报告只对送检样品检测结果负责。以上检测结果为快速检测定性检测结果，不具备法律效力，仅供客户参考。</br>
                    4、委托单位对本检测报告有异议，请在收到报告之日或指定领取报告之日起，三个工作日内提出申诉，逾期不子受理。</div>
                        </div>

                        <!--?印章-->
                        <div style="background-color: aquamarine; width: 95%; font-size: 16px; font-weight: bold; margin-bottom: 10%; position: relative" :style="{ 'margin-top': v.sample_list.length > pageSzie ? '-2px' : '2px' }">
                            <div style="z-index: 999; position: absolute; transform: translateX(-140%) translateY(-50%); margin-left: 100%; background-color: transparent">
                                <img src="@/static/gz.png" width="150px;" height="150px;" />
                            </div>
                            <div style="margin-left: 100%; z-index: 2; white-space: nowrap; position: absolute; transform: translateX(-100%); font-family: 黑体">此处未盖本公司快检专用章，则本报告无效。</div>
                        </div>
                        <!--?印章-->
                        <div style="display: flex; flex-direction: row; justify-content: space-between; width: 95%; z-index: 1">
                            <div style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                                <div>检测人:</div>
                                <div style="position: absolute; left: 28%; top: -80%">
                                    <span>
                                        <img v-if="$fn.isNotNull(v.detection_user_info.sign_image)" width="100px" height="40px" :src="v.detection_user_info.sign_image" />
                                    </span>
                                </div>
                            </div>
                            <div style="width: 33%; font-size: 16px; font-weight: bold; font-family: 黑体; display: flex; position: relative">
                                <div>审核人:</div>
                                <div style="position: absolute; left: 28%; top: -60%">
                                    <img v-if="$fn.isNotNull(v.detection_user_info.reviewer_sign_image)"
                                         :src="v.detection_user_info.reviewer_sign_image"
                                         width="100px" height="40px" />
                                    <img v-else src="@/static/shr.png" width="100px" height="40px" />
                                </div>
                            </div>
                            <div style="width: 34%; font-size: 16px; font-weight: bold; font-family: 黑体">
                                <span>签发日期:</span>
                                <span>{{ vk.pubfn.timeFormat(v.detect_time, 'yyyy年MM月dd日') }}</span>
                            </div>
                        </div>

        <!-- 修改页面底部元素的显示逻辑 -->
                        <div style="margin-top: auto; width: 95%; padding-bottom: 20px;">
                            <!-- 修改页码显示逻辑 -->
                            <div style="display: flex; justify-content: center; margin-bottom: 10px; text-align: center; font-size: 10px;" 
                                 v-if="shouldShowPageNumber(v.sample_list.length)">
                                {{ getPageNumberText(v.sample_list.length) }}
                            </div>
                            
                            <!-- 报告完结只在最后一页显示 -->
                            <div style="margin-top: 5px; margin-bottom: 10px; text-align: center; font-size: 18px"
                                 v-if="isLastPage(v.sample_list.length)">
                                ***报告完结***
                            </div>
                            
                            <!-- 页脚分隔线和信息 -->
                            <div style="padding: 0px; background-color: black; margin-top: 3px; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 2px"></div>
                            <div style="padding: 0px; background-color: black; margin-top: 5px; font-weight: bold; font-weight: bold; border-bottom-color: #000; border-bottom-style: solid; border-bottom-width: 1px"></div>
                            <div style="display: flex; align-content: space-between; color: #606266; margin-top: 5px">
                                <div style="font-size: 12px; font-family: 黑体; display: ruby-text">Hotline 0755-28380866 深圳市金阅检测科技有限责任公司</div> 
                                <div style="margin-left: 16%; font-size: 12px; text-align: end; font-family: 黑体; align-content: flex-end; color: #606266">地址：深圳市大鹏新区大鹏街道布新社区布新村工业大道2号C401</div>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
           
        </div>

        <template v-slot:footer="{ close }">
            <!--这里是底部插槽-->
            <el-button @click="advancedPrint">打 印</el-button>
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="close">确 定</el-button>
        </template>
    </vk-data-dialog>
</template>

<script>
import VueQr from 'vue-qr';
import Print from 'print-js';
import print_content from './print_content.vue';
import print_footer from './print_footer.vue';
import printbase from '@/mixins/printbase.js';
import { slice } from 'lodash';
var that; // 当前页面对象
var vk = uni.vk; // vk实例
export default {
    components: {
        VueQr,
        print_content,
        print_footer,

    },
    props: {
        value: {
            Type: Object,
            default: function () {
                return {
                    show: false,
                    printData: []
                };
            }
        }
    },
        mixins: [ printbase],
    data: function () {
        // 组件创建时,进行数据初始化
        return {

        };
    },
    mounted() {
        that = this;

        that.init();
    },

    methods: {

        getremarkTitle(list) {
            if (vk.pubfn.isNotNull(list)&&list.length > 0) {
                if (list[0].detection_category == '农药残留' || list[0].detection_category == '有机磷')
                return '抑制率'
            }
            return '备注'
            
        },
             getremark(form) {
            if (vk.pubfn.isNotNull(form.sample_list)&&form.sample_list.length > 0&&vk.pubfn.isNotNull(form.remark)) {
                if (form.sample_list[0].detection_category == '农药残留' || form.sample_list[0].detection_category == '有机磷')
                return form.remark
            }
            return ''
            
        },


    },
    // 监听属性
    watch: {
        'value.printData'(val) {
            val.forEach(element => {
                const sampleLength = element.sample_list.length;
                let targetLength = 0;
                
                // 确定目标长度
                if (sampleLength < 10) {
                    targetLength = 10;
                } else {
                    // 找到下一个10的倍数，但不超过200
                    targetLength = Math.min(Math.ceil(sampleLength / 10) * 10, 200);
                }
                
                // 添加空白行
                if (sampleLength < targetLength) {
                    const rowsToAdd = targetLength - sampleLength;
                    for (let i = 0; i < rowsToAdd; i++) {
                        element.sample_list.push({
                            sampleno: i == 0 ? '以下空白' : ''
                        });
                    }
                }
            });
        }
    },
    // 计算属性
    computed: {
        first15SampleList() {
            return this.sample_list.slice(0, 15);
        },
        // 使用计算属性来获取剩余的样本
        remainingSampleList() {
            return this.sample_list.slice(15);
        }
    }
};
</script>

<style lang="scss" scoped>
// .daying {
//     border-spacing: 0;
//     width: 100%;
//     border-collapse: collapse;
//     font-family: 宋体;
//     color: #000
// }

// .daying td {
//     border: 1px solid #000;
//     height: 15px
// }

// .daying td label {
//     font-size: 14px
// }

// .daying td span {
//     font-size: 16px
// }

@media print {
    .last-page {
        page-break-after: avoid !important;
    }
}

.page-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
}

.watermark-item {
    position: absolute;
    transform: rotate(-30deg);
    opacity: 0.1;
    font-size: 40px;
    font-weight: bold;
    color: #000;
    white-space: nowrap;
}
</style>


