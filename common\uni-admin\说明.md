### 此目录为 `uni-admin` 官方模板的一些样式文件

* 如果你需要引入 `uni-admin` 官方的一些插件，如[升级中心](https://ext.dcloud.net.cn/plugin?id=4470)
* 你可能会碰到样式错乱的问题，那是因为 `vk-uni-admin` 并没有引入 `uni-admin` 的公共样式
* 但框架已经帮你整理好了公共样式的文件，就在 `common/uni-admin` 目录内

### 在 `App.vue` 中引入 `uni-admin` 的公共样式

```html
<style lang="scss">
	@import '@/common/uni-admin/css/uni.css';
	@import '@/common/uni-admin/css/uni-icons.css';
</style>

```


### 同时官方的插件一般是不通过 `云函数` 获取数据的，而是通过 `clientDB` 操作数据库（前端操作数据库）

* 因此，你需要上传一些数据库的 `schema.json` 文件

### 如 `升级中心` 需要上传以下两个 `schema.json` 文件

**特别注意：如果 `admin项目` 是绑定 `client项目`，则需要复制到 `client项目` 的 `uniCloud/database/`目录去上传**

* opendb-app-list.schema.json
* opendb-app-versions.schema.json

#### 这些 `schema.json` 文件一般在插件包里就有，直接右键点击对应的文件即可上传。

* 如升级中心的 `schema.json` 目录在 `uni_modules/uni-upgrade-center/uniCloud/database/opendb-app-list.schema.json`

* 同时一般插件还会有一个自己的 `db_init.json` 文件 如 `uni_modules/uni-upgrade-center/uniCloud/database/db_init.json` （也是右键上传）

* 如还有样式问题，建议重启项目试试。