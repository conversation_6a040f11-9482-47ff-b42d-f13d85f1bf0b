<template>
	<view class="page-body">
		<!-- 页面内容开始 -->

		<!-- 表格搜索组件开始 -->
		<vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" size="mini"
			@search="search"></vk-data-table-query>
		<!-- 表格搜索组件结束 -->

		<!-- 自定义按钮区域开始 -->
		<view>
			<el-row>
				<el-button type="success" size="mini" icon="el-icon-circle-plus-outline" @click="addBtn">添加</el-button>


				<!-- 批量操作 -->
				<el-dropdown v-if="table1.multipleSelection" :split-button="false" trigger="click" @command="batchBtn">
					<el-button type="danger" size="mini" style="margin-left: 20rpx;"
						:disabled="table1.multipleSelection.length === 0">
						批量操作<i class="el-icon-arrow-down el-icon--right"></i>
					</el-button>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item :command="1">批量操作1</el-dropdown-item>
						<el-dropdown-item :command="2">批量操作2</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</el-row>
		</view>
		<!-- 自定义按钮区域结束 -->

		<!-- 表格组件开始 -->
		<vk-data-table ref="table1" :rowNo="true" :border="true" :action="table1.action" :columns="table1.columns"
			:query-form-param="queryForm1" size="mini" :right-btns="['detail_auto', 'update', 'delete']"
			:selection="true" :pagination="true" @update="updateBtn" @delete="deleteBtn" @current-change="currentChange"
			@selection-change="selectionChange"></vk-data-table>
		<!-- 表格组件结束 -->

		<!-- 添加或编辑的弹窗开始 -->
		<vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="850px" mode="form"
			:close-on-click-modal="false">
			<vk-data-form v-model="form1.data" ref="form1" :rules="form1.props.rules" :action="form1.props.action"
				:form-type="form1.props.formType" :columns='form1.props.columns' label-width="120px"
				@success="form1.props.show = false; refresh();"></vk-data-form>
		</vk-data-dialog>

		<!-- 添加或编辑的弹窗结束 -->

		<!-- 页面内容结束 -->
	</view>
</template>

<script>
var that;													// 当前页面对象
var vk = uni.vk;									// vk实例
var originalForms = {};						// 表单初始化数据

export default {
	data() {
		// 页面数据变量
		return {
			// 页面是否请求中或加载中
			loading: false,
			// init请求返回的数据
			data: {

			},

			// 表格相关开始 -----------------------------------------------------------
			table1: {
				// 表格数据请求地址
				action: "admin/base/detection_category/sys/getList",
				// 表格字段显示规则
				columns: [
					// { key:"_id", title:"id", type:"text", width:220 },

					{ key: "name", title: "检测项目", type: "text", width: 250, sortable: "custom" },
					{ key: "standard", title: "检测标准", type: "text", width: 250, },
					{ key: "detection_include", title: "检测限", type: "text", width: 350, },
					{ key: "standardRequir", title: "检测要求", type: "text", needSave: true, },


					{
						key: "is_on", title: "是否启用", width: 80, type: "switch", width: 120, activeValue: true, inactiveValue: false,

						watch: (res) => {
							let { value, row, change } = res;
							vk.callFunction({
								url: "admin/base/detection_category/sys/update",
								title: value ? "启用中..." : "关闭中...",
								data: {
									_id: row._id,
									is_on: value
								},
								success: data => {
									change(value);
								}
							});
						}
					},
					{ key: "sort", title: "排序", type: "number", width: 100, sortable: "custom" },
					// { key:"_add_time", title:"添加时间", type:"time", width:160, sortable:"custom"  },
					// { key:"_add_time", title:"距离现在", type:"dateDiff", width:120 },
				],
				// 多选框选中的值
				multipleSelection: [],
				// 当前高亮的记录
				selectItem: ""
			},
			// 表格相关结束 -----------------------------------------------------------
			// 表单相关开始 -----------------------------------------------------------
			// 查询表单请求数据
			queryForm1: {
				// 查询表单数据源，可在此设置默认值
				formData: {

				},
				// 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
				columns: [
					{ key: "name", title: "检测项目", type: "text", width: 160, mode: "%%" },

				]
			},
			form1: {
				// 表单请求数据，此处可以设置默认值
				data: {
is_on:true,
				},
				// 表单属性
				props: {
					// 表单请求地址
					action: "",

					// 表单字段显示规则
					columns: [

						{ key: "name", title: "检测项目", type: "text", placeholder: "请输入类别名称" },
						{ key: "standard", title: "检测标准", type: "text", needSave: true, },
						{ key: "standardRequir", title: "检测要求", type: "text", needSave: true, },
						// {
						// 	key: "detection_include",
						// 	title: "检测限",
						// 	type: "array<object>",
						// 	itemWidth: 400,
						// 	showAdd: true,
						// 	showClear: true,
						// 	showSort: true,
						// 	// 新增一行时,该行的默认值
						// 	defaultValue: {
						// 		value: ""
						// 	},
						// 	rightBtns: [ 'delete'],
						// 	// 每行每个字段对应的渲染规则
						// 	columns: [{
						// 		key: "value",
						// 		title: "检测限名称",
						// 		type: "text",
						// 		isUnique: true,
						// 		rules: [{
						// 			required: true,
						// 			message: "该项不能为空",
						// 			trigger: ["change", "blur"]
						// 		}], width: '200'
						// 	}
						// 	]
						// },
						{
							key: "detection_include", title: "检测限", type: "textarea",
							autosize: { minRows: 4, maxRows: 10 },
							maxlength: 200,
							showWordLimit: true, needSave: true,
						},
						{
							key: "is_on", title: "是否启用",  type: "switch", width: 120, activeValue: true, inactiveValue: false,
						},
						{ key: "sort", title: "排序", type: "number", placeholder: "请输入排序权重", width: 100 },
					],
					// 表单验证规则
					rules: {
						name: [
							// 必填
							{ required: true, message: "检测项目不能为空", trigger: ['blur', 'change'] }
						],
						standard: [
							// 必填
							{ required: true, message: "检测标准不能为空", trigger: ['blur', 'change'] }
						],
						// sort: [
						// 	// 必填
						// 	{ required: true, message: "排序不能为空", trigger: ['blur', 'change'] }
						// ],
					},
					// add 代表添加 update 代表修改
					formType: "",
					// 是否显示表单的弹窗
					show: false
				}
			},
			// 其他弹窗表单
			formDatas: {},
			// 表单相关结束 -----------------------------------------------------------
		};
	},
	// 监听 - 页面每次【加载时】执行(如：前进)
	onLoad(options = {}) {
		that = this;
		vk = that.vk;
		that.options = options;
		that.init(options);
	},
	onUnload() {
		// 返回false阻止页面被销毁
		return false;
	},
	// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
	onReady() { },
	// 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
	onShow() { },
	// 监听 - 页面每次【隐藏时】执行(如：返回)
	onHide() { },
	// 函数
	methods: {
		// 页面数据初始化函数
		init(options) {
			originalForms["form1"] = vk.pubfn.copyObject(that.form1);
		},
		// 页面跳转
		pageTo(path) {
			vk.navigateTo(path);
		},
		// 表单重置
		resetForm() {
			vk.pubfn.resetForm(originalForms, that);
		},
		// 搜索
		search() {
			that.$refs.table1.search();
		},
		// 刷新
		refresh() {
			that.$refs.table1.refresh();
		},
		// 获取当前选中的行的数据
		getCurrentRow() {
			return that.$refs.table1.getCurrentRow();
		},
		// 监听 - 行的选中高亮事件
		currentChange(val) {
			that.table1.selectItem = val;
		},
		// 当选择项发生变化时会触发该事件
		selectionChange(list) {
			that.table1.multipleSelection = list;
		},
		// 显示添加页面
		addBtn() {
			that.resetForm();
			that.form1.props.action = 'admin/base/detection_category/sys/add';
			that.form1.props.formType = 'add';
			that.form1.props.title = '添加';
			that.form1.props.show = true;
		},
		// 显示修改页面
		updateBtn({ item }) {
			that.form1.props.action = 'admin/base/detection_category/sys/update';
			that.form1.props.formType = 'update';
			that.form1.props.title = '编辑';
			that.form1.props.show = true;
			that.form1.data = item;
		},
		// 删除按钮
		deleteBtn({ item, deleteFn }) {
			deleteFn({
				action: "admin/base/detection_category/sys/delete",
				data: {
					_id: item._id
				},
			});
		},
		// 监听 - 批量操作的按钮点击事件
		batchBtn(index) {
			switch (index) {
				case 1: vk.toast("批量操作按钮1"); break;
				case 2: vk.toast("批量操作按钮2"); break;
				default: break;
			}
		},

	},
	// 监听属性
	watch: {

	},
	// 过滤器
	filters: {

	},
	// 计算属性
	computed: {

	}
};
</script>

<style lang="scss" scoped>
.page-body {}
</style>
