module.exports = {
    /**
     * 批量修改用户状态
     * @url admin/bidding-booths/sys/batchUpdateBiddingTime 前端调用的url参数地址
     * data 请求参数 说明
     * @param {Array} user_ids 		用户id数组
     * @param {Integer} status 		修改成的用户状态：0 正常 1 禁用 2 审核中 3 审核拒绝
     * res 返回参数说明
     * @param {Number} code 错误码，0表示成功
     * @param {String} msg 详细信息
     */
    main: async (event) => {
        let { data = {}, userInfo, util, filterResponse, originalParam } = event;
        let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
        let { uid } = data;
        let res = { code: 0, msg: '' };
        // 业务逻辑开始-----------------------------------------------------------
        let { batch_datas } = data;

        for (let index = 0; index < batch_datas.length; index++) {
            const _addform = batch_datas[index];
            let info = await vk.baseDao.findByWhereJson({
                dbName: "uni-id-users",
                whereJson: {
                    nickname: _addform.detection_user,
                }
            });
            let detection_user_sign = undefined;
            if (vk.pubfn.isNotNull(info) && vk.pubfn.isNotNull(info.sign_image)) {
                detection_user_sign = info.sign_image
            }
            let sample_list=  JSON.parse(JSON.stringify(_addform.sample_list))
            delete _addform.sample_list;
            let _addid = await vk.baseDao.add({
                dbName:'tm-detection-form',
                dataJson: {
                    no: vk.pubfn.createOrderNo("", 17),
                    detection_user_sign: detection_user_sign,
                    ..._addform
                }
            });
            if (sample_list != undefined && sample_list.length > 0) {
                let _add_sample_list = sample_list.map(x => {
                    return {
                        'detectionform_id': _addid,
                        'client': _addform.client,
                        'client_name': _addform.client_name,
                        ...x
                    }
                })
                let ids = await vk.baseDao.adds({
                    dbName: "tm-sample-testing",// 表名
                    dataJson: _add_sample_list
                });
            }
    }
        // // 执行数据库API请求
        // res.count = await vk.baseDao.adds({
        //     dbName: "tm-detection-form",// 表名
        //     dataJson: batch_booths
        // });
        return res;
    }

}